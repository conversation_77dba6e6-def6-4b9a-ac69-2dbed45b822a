document.head.insertAdjacentHTML("beforeend", `<link href="https://assets.mirus.help/virex/b/css/comm-form.css" rel="stylesheet" type="text/css">`);

class Comment {
  constructor(
    commentForm,
    inputCommentName,
    inputCommentText,
    formImage,
    formAvatar,
    commentPushBlock
  ) {
    this.commentForm = document.querySelector(commentForm);
    this.inputCommentName = document.querySelector(inputCommentName);
    this.inputCommentText = document.querySelector(inputCommentText);
    this.formImage = document.querySelector(formImage);
    this.formAvatar = document.querySelector(formAvatar);
    this.commentPushBlock = document.querySelector(commentPushBlock);
    this.commArrAll = [];
    this.formImageUrl;
    this.formImageChange();
    this.pushComBlock();
    this.domOnloader();
  }

  uploadFile(file) {
    // Проверяем тип файла
    if (!["image/jpeg", "image/png", "image/gif"].includes(file.type)) {
      alert("Only images are allowed.");
      formImage.value = "";
      return;
    }
    // Проверяем размер файла (<2 Мб)
    if (file.size > 1 * 1024 * 1024) {
      alert("The file must be less than 1 MB.");
      return;
    }
    var reader = new FileReader();
    reader.onload = (e) => {
      this.formAvatar.innerHTML = `<img src="${e.target.result}" alt="avatar">`;
      this.formAvatar.classList.add("form__avatar--loaded");
      this.formImageUrl = e.target.result;
    };
    reader.onerror = function (e) {
      alert("Error");
    };
    reader.readAsDataURL(file);
  }

  formImageChange() {
    this.formImage.addEventListener("change", () => {
      this.uploadFile(this.formImage.files[0]);
    });
  }

  pushComm() {}

  removeInputClass() {
    if (this.inputCommentName) {
      this.inputCommentName.value = "";
      this.inputCommentName.classList.remove("error");
    }
    this.inputCommentText.value = "";
    this.inputCommentText.classList.remove("error");
  }

  pushComBlock() {
    this.commentForm.addEventListener("submit", (e) => {
      e.preventDefault();
      if (this.inputCommentName) {
        if (this.inputCommentName.value && this.inputCommentText.value) {
          this.formAvatar.innerHTML = "";
          this.formAvatar.classList.remove("form__avatar--loaded");
          return this.pushComm();
        }
        this.inputCommentName.classList.add("error");
        this.inputCommentText.classList.add("error");
      } else {
        if (this.inputCommentText.value) {
          this.formAvatar.innerHTML = "";
          this.formAvatar.classList.remove("form__avatar--loaded");
          return this.pushComm();
        }
        this.inputCommentText.classList.add("error");
      }
    });
  }

  domOnloader() {
    const self = this; // Сохраняем ссылку на текущий объект Comment
    document.addEventListener("DOMContentLoaded", () => {
      let commArr = localStorage["commArr"];
      if (commArr) {
        self.commArrAll = JSON.parse(localStorage.getItem("commArr"));
        self.commentPushBlock.innerHTML = self.commArrAll.join("");
      }
    });
  }
}

// Устанавливаем испанские плейсхолдеры напрямую при загрузке страницы
document.addEventListener("DOMContentLoaded", function() {
  document.getElementById("inputCommentName").setAttribute("placeholder", "Nombre");
  document.getElementById("inputCommentText").setAttribute("placeholder", "Tu comentario");
  document.getElementById("commentPush").innerText = "Enviar";
});

// Создаем объект комментария
let comment = new Comment(
  "#commentForm",
  "#inputCommentName",
  "#inputCommentText",
  "#formImage",
  "#formAvatar",
  "#commentPushBlock"
);
Comment.prototype.pushComm = function () {
  let urlAvatar = "";
  this.formImageUrl ? (urlAvatar = this.formImageUrl) : (urlAvatar = "https://assets.mirus.help/virex/b/comm_form/userpic.png"); // проверяем путь к дефолтной картинке

  const commentDate = new Date(Date.now() - 1000 * 60 * 60 * 24 * 0).toLocaleDateString("ru-RU");

  //! сюда вставляем разметку коммента с ленда и подставляем значения
  const comment = `
          <div class="item inL_634686">
            <div class="component_ava inL_611767" style="background-image: url(${urlAvatar});"></div>
            <div class="component_body">
              <div class="component_info">
                <div class="component_info_inner">
                  <div class="component_name">
                    <p>${this.inputCommentName.value}</p>
                  </div>
                  <div class="component_text">
                    <p>
                       ${this.inputCommentText.value}
                    </p>
                  </div>
                </div>
                <div class="component_licked inL_214367">
                  <span class="icons"></span>
                  <span class="popular"></span>
                </div>
              </div>
              <div class="component_reposy">
                <b>${commentDate}</b>
                <nav>Me gusta</nav>
                <nav>Responder</nav>
                <nav>Más</nav>
              </div>
            </div>
          </div>
        `;
  this.removeInputClass();
  this.commArrAll.push(comment);
  this.commentPushBlock.innerHTML = this.commArrAll.join("");
  localStorage.setItem("commArr", JSON.stringify(this.commArrAll));
  this.formAvatar.innerHTML = "";
  this.formImageUrl = "";
  urlAvatar = "";
};

//! этот блок вставляем в html код после окончания комментариев

/*

<div id="commentPushBlock"></div>

<form id="commentForm">
    <div class="form__item">
        <div class="file">
            <div class="file__item">
                <label for="formImage" class="form__avatar" id="formAvatar"></label>
                <input id="formImage" accept=".jpg, .png, .gif" type="file" name="image" class="file__input">
            </div>
        </div>
    </div>
    <div class="form__inputs">
        <input type="text" placeholder="Имя" id="inputCommentName">
        <textarea name="" id="inputCommentText" placeholder="Комментарий" cols="20" rows="5"></textarea>
        <button type="submit" id="commentPush">
            <!-- <img src="img/go.png" alt=""> -->
            <span>SEND</span>
        </button>
    </div>
</form>

*/

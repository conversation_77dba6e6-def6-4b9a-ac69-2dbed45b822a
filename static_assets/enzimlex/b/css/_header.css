.header {
  background-color: #fff;
  border-bottom: 1px solid #E7E7E7;
}

.header__container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: auto;
  margin-right: auto;
  padding: 1rem;
}

.header .panel-panel {
  padding-top: 16px;
  padding-bottom: 16px;
}

.header #block-ft-common-donate a {
  right: 20px;
  float: right;
  font-family: 'krona_oneregular';
  border-radius: 24px;
  font-size: 1.3rem;
  line-height: 34px;
  padding: 0px 20px;
  position: relative;
  text-transform: uppercase;
  background-color: #FE5E00;
  color: #fff;
  -webkit-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.header .navbar-toggle {
  position: relative;
  /* float: right; */
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}

.navbar-toggle .icon-bar {
  background-color: #050505;
  height: 4px;
  width: 25px;
  display: block;
  border-radius: 2px;
}

.navbar-toggle .icon-bar:not(:last-child) {
  margin-bottom: 4px;
}


.navbar-toggle:after {
  content: " MENU ";
  color: #050505;
  float: left;
  margin-left: 35px;
  margin-top: -22px;
  font-size: 1.1rem;
}

.header .logo img {
  width: 80px;
  height: 80px;
}

@media screen and (max-width:639px) {
  .header #block-ft-common-donate a {
    font-size: 1rem;
    padding-left: 10px;
    padding-right: 10px;
  }

  .navbar-toggle:after {
    content: none;
  }
}
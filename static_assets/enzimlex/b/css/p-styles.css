@import url("https://assets.mirus.help/enzimlex/b/css/_fonts.css");
@import url("https://assets.mirus.help/enzimlex/b/css/_header.css");
@import url("https://assets.mirus.help/enzimlex/b/css/_footer.css");
@keyframes ScaleRotate {
  0% {
    transform: scale(0.9) rotate(35deg);
  }

  50% {
    transform: scale(1.2) rotate(15deg);
  }

  100% {
    transform: scale(0.9) rotate(35deg);
  }
}

@keyframes ScaleRotate {
  0% {
    transform: scale(0.9) rotate(35deg);
  }

  50% {
    transform: scale(1.2) rotate(15deg);
  }

  100% {
    transform: scale(0.9) rotate(35deg);
  }
}

@keyframes blink {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes blink {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes Scale {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.03);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes Scale {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.03);
  }

  100% {
    transform: scale(1);
  }
}

.dialog {
  max-width: 1200px;
  margin: 20px auto;
}

.comments__wrapper {
  display: flex;
  margin-bottom: 20px;
}

.comments__ava {
  display: inline-block;
  margin-right: 10px;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  object-fit: cover;
}

.comments__text {
  padding: 10px;
}

.display {
  display: flex;
  flex-direction: column;
}

.main-form__exclusive {
  position: absolute;
  left: -5px;
  bottom: calc(100% - 35px);
  width: 160px;
  height: auto;
}

.main-form {
  --pr-color: 7, 90, 168;
  --product-color: rgb(var(--pr-color));
  --product-color-transp: rgba(var(--pr-color), 0.2);
  --radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  margin-top: 60px;
  margin-bottom: 20px;
  gap: 60px 20px;
}

.main-form__left {
  position: relative;
  max-width: 250px;
}

.main-form__discount-wrap {
  --size-discount: 100px;
  position: absolute;
  left: calc(100% - 80px);
  bottom: calc(100% - 40px);
  width: var(--size-discount);
  height: var(--size-discount);
  transform: rotate(20deg);
  -webkit-animation: 5s linear infinite scalerotate;
  animation: 5s linear infinite ScaleRotate;
}

.main-form__sticker {
  width: 100%;
  height: 100%;
  fill: rgb(224, 62, 82);
}

.main-form__discount {
  position: absolute;
  left: 50%;
  top: 50%;
  margin: 0;
  font-size: 28px !important;
  line-height: 1.02;
  color: #fff;
  transform: translate(-50%, -50%);
}

.main-form__right {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: var(--radius);
  padding: 30px 20px;
  min-width: 300px;
  background-color: var(--product-color-transp);
}

.main-form__prices {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  font-size: 18px;
  gap: 20px;
}

.main-form__price {
  display: flex;
  flex-direction: column;
  margin-bottom: 0 !important;
  text-transform: uppercase;
}

.main-form__price--new {
  font-weight: 700 !important;
  color: var(--product-color) !important;
}

.main-form__price--new>span {
  font-size: 34px;
  line-height: 2rem;
}

.main-form__price--old {
  opacity: 0.8;
}

.main-form__price--old>span {
  font-size: 28px;
  text-decoration: line-through;
  line-height: 2rem;
}

.main-form__group {
  margin-bottom: 20px;
}

.main-form__input {
  display: block;
  border: 2px solid var(--product-color);
  border-radius: var(--radius);
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
  font-family: inherit;
  font-size: 20px;
}

.main-form__img {
  max-height: 300px;
}

.main-form__btn {
  margin-bottom: 10px;
  border: 2px solid transparent;
  border-radius: var(--radius);
  padding: 10px 20px;
  font-weight: 700;
  font-size: 18px;
  text-transform: uppercase;
  color: #fff;
  background-color: var(--product-color);
  transition-property: background-color, color, border-color;
  transition-duration: 0.3s;
  transition-timing-function: ease-in-out;
  cursor: pointer;
}

.main-form__btn:focus,
.main-form__btn:hover {
  -webkit-animation: 3s linear infinite scale;
  animation: 3s linear infinite Scale;
}

.main-form__btn:hover,
.main-form__btn:focus {
  border-color: var(--product-color);
  color: var(--product-color);
  background-color: transparent;
}

@media screen and (min-width: 770px) {
  .main-form {
    flex-direction: row;
  }

  .main-form__left {
    max-width: 300px;
  }
}

.timer {
  display: flex;
  justify-content: space-between;
  margin-right: auto;
  margin-bottom: 32px;
  margin-left: auto;
  max-width: 180px;
  font-family: "Calibri", sans-serif;
}

.timer__divider {
  font-size: 30px;
  transform: translateY(4px);
  -webkit-animation: 1s linear infinite blink;
  animation: 1s linear infinite blink;
}

.timer__wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.timer__wrapper>span {
  display: block;
  flex-grow: 0;
  flex-shrink: 0;
  border-radius: 8px;
  width: 40px;
  height: 45px;
  font-weight: 700;
  font-size: 22px;
  line-height: 45px;
  text-align: center;
  color: #fff;
  background-color: var(--product-color);
}

.timer__wrapper>p {
  position: absolute;
  left: 50%;
  top: calc(100% + 1px);
  margin: 0;
  font-size: 15px;
  transform: translateX(-50%);
}

.p-block-col {
  margin: 50px auto;
}

.p-block-col__title {
  position: relative;
  margin: 0;
  margin-bottom: 20px;
  font-size: 26px;
  font-weight: 600;
  color: rgb(75, 79, 84);
}

.p-block-col__title::before {
  content: "";
  position: absolute;
  left: 0;
  top: calc(100% + 2px);
  width: 100%;
  height: 2px;
  background-color: rgb(1, 178, 170);
}

.list-reset {
  list-style: none;
  margin: 0;
  padding: 0;
}

.p-block-col__list {
  display: flex;
  justify-content: space-between;
}

.p-block-col__list--column {
  flex-direction: column;
}

.p-block-col__item {
  display: flex;
  flex-direction: column;
  width: calc(50% - 20px);
}

.p-block-col__item--row {
  flex-direction: row;
  align-items: center;
  width: auto;
}

.p-block-col__item--row:not(:last-child) {
  margin-bottom: 20px;
}

.p-block-col__heading {
  position: relative;
  align-self: flex-start;
  margin: 0 0 20px;
  font-size: 22px;
  font-weight: 600;
  color: rgb(75, 79, 84);
}

.p-block-col__heading::before {
  content: "";
  position: absolute;
  left: 0;
  top: calc(100% + 2px);
  width: calc(100% + 40px);
  height: 2px;
  background-color: rgb(224, 62, 82);
}

.p-block-col__heading--min {
  flex: 0 0 145px;
  align-self: center;
  margin-right: 10px;
}

.p-block-col__heading--min::before {
  width: 95%;
}

.p-block-col__icon {
  flex: 0 0 45px;
  margin-right: 20px;
  width: 45px;
  max-height: 60px;
}

.p-block-col__wrapper {
  display: flex;
}

.p-block-col__content {
  position: relative;
  flex-grow: 1;
  margin: 0;
  font-size: 20px;
}

@media screen and (max-width: 1000px) {
  .p-block-col__list {
    flex-direction: column;
  }

  .p-block-col__wrapper {
    flex-wrap: wrap;
  }

  .p-block-col__icon {
    flex-basis: 70px;
    width: 70px;
    max-height: 90px;
  }

  .p-block-col__item {
    width: 100%;
  }

  .p-block-col__item:not(:last-child) {
    margin-bottom: 40px;
  }
}

@media all {
  * {
    padding: 0;
    margin: 0;
  }

  a:focus {
    outline: 0;
  }

  *,
  :after,
  :before {
    box-sizing: inherit;
  }

  ::-moz-selection {
    background: #ea5b0c;
    color: #fff;
  }

  ::selection {
    background: #ea5b0c;
    color: #fff;
  }

  input {
    display: inline-block;
    padding: .6rem .8rem;
    border: solid 1px #96a0ae;
    border-radius: 2px;
    outline: 0;
  }

  .row {
    box-sizing: border-box;
    display: -moz-flex;
    display: flex;
    -moz-flex: 0 1 auto;
    flex: 0 1 auto;
    -moz-flex-direction: row;
    flex-direction: row;
    flex-wrap: wrap;
    margin-right: -.5rem;
    margin-left: -.5rem;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  form,
  input {
    font: inherit;
  }

  a {
    text-decoration: none;
  }

  li {
    list-style-position: outside;
  }

  ul li {
    list-style-type: disc;
  }
}

/*! CSS Used from: http://cardioactive4mexico/css/ehwOikM2EXmw.css */
div,
span,
h1,
h3,
p,
img,
b,
figure {
  margin: 0px;
  padding: 0px;
  border: 0px none;
  outline: 0px none;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent none repeat scroll 0% 0%;
}

figure {
  display: block;
}

a {
  margin: 0px;
  padding: 0px;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent none repeat scroll 0% 0%;
}

* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
}

a {
  color: rgb(184, 139, 88);
  text-decoration: none;
}

a.button {
  font-size: 20px;
  text-align: center;
  color: rgb(255, 255, 255) !important;
  background-color: red;
  display: block;
  width: 100%;
  max-width: 320px;
  border-radius: 50px;
  text-decoration: none;
  padding: 15px 20px;
}

a.button:hover {
  background-color: rgb(193, 31, 31);
}

figure,
section {
  display: block;
}

a {
  background: transparent none repeat scroll 0% 0%;
}

a:active,
a:hover {
  outline: 0px none;
}

b {
  font-weight: 700;
}

h1 {
  font-size: 2em;
  margin: 0.67em 0px;
}

img {
  border: 0px none;
}

svg:not(:root) {
  overflow: hidden;
}

figure {
  margin: 1em 40px;
}

button,
input {
  color: inherit;
  font: inherit;
  margin: 0px;
}

button {
  overflow: visible;
}

button {
  text-transform: none;
}

button {
  cursor: pointer;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0px none;
  padding: 0px;
}

input {
  line-height: normal;
}

/*! CSS Used from: http://cardioactive4mexico/css/ej97o5tnoosO.css */
h1 {
  color: #333333;
  font-size: 38px;
  font-weight: 300;
}

h1,
p {
  margin-bottom: 20px;
}

@media all and (max-width: 1023px) {
  h1 {
    font-size: 27px;
  }
}

.timer {
  font-size: 18px;
  font-weight: normal;
  margin: 20px auto;
  text-align: center;
}

::-ms-clear {
  display: none;
}

/*! CSS Used from: http://cardioactive4mexico/css/index.css */
a {
  text-decoration: none;
}

.pg {
  font-size: 22px !important;
  line-height: 36px !important;
  margin-bottom: 33px;
}

#comments {
  margin-top: 20px;
}

#comments .pg {
  font-size: 18px !important;
  line-height: 24px !important;
  color:  #000 !important;
}

#comments h2 {
  padding-left: 40px;
  color: #000;
  border-bottom: 0px solid;
  border-color: #000;
  text-transform: uppercase;
  padding-bottom: 10px;
}

#comments .coment_ava {
  float: left;
  margin-right: 15px;
}

#comments .coment_ava img {
  margin: 0px;
  width: 50px;
  height: 50px;
  border-radius: 5px;
}

@media screen and (max-width: 560px) {
  .pg {
    margin-left: 0px;
    margin-right: 0px;
  }
}

a:active,
a:focus {
  outline: none;
}

input {
  outline: none;
}

input:active {
  outline: none;
}

:focus {
  outline: none;
}

button:active,
button:focus {
  outline: none !important;
}

button::-moz-focus-inner {
  border: 0 !important;
}

.randdate {
  font-size: 14px;
}

a.button {
  font-size: 120%;
  text-align: center;
  color: #fff !important;
  background-color: rgb(13,148,136);
  display: table;
  margin: 25px auto;
  width: 400px;
  max-width: 100%;
  border-radius: 15px;
  text-decoration: none;
  padding: 15px;
}

a.button:hover {
  background-color: #c11f1f;
}

@media (max-width: 600px) {
  a.button {
    width: 80%;
  }
}

/*! CSS Used from: http://cardioactive4mexico/css/style.css */
*,
*::before,
*::after {
  box-sizing: border-box;
}

figure,
section {
  display: block;
}

h1,
h2,
h3 {
  margin-top: 0;
  margin-bottom: .5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}


a {
  color: #000;
  -webkit-text-decoration: #000;
  text-decoration: #000;
  background-color: transparent;
}

a:hover {
  color: #0056b3;
  text-decoration: underline;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
  border-style: none;
}

svg {
  overflow: hidden;
  vertical-align: middle;
}

button {
  border-radius: 0;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

input,
button {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button {
  text-transform: none;
}

button,
[type="submit"] {
  -webkit-appearance: button;
}

button::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

h1,
h2,
h3 {
  margin-bottom: .5rem;
  font-weight: 500;
  line-height: 1.2;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.75rem;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col-12,
.col-md-12 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

a:hover {
  text-decoration: none;
}

body button:focus {
  outline: 0;
}

.page-internal-content h2,
.page-internal-content h3 {
  font-weight: 700;
}

.page-internal-content h2 {
  font-size: 1.3rem;
}

.page-internal-content h3 {
  font-size: 1.23rem;
}

.page-internal-content b {
  font-weight: 600;
}

.aspectRatio-picture {
  overflow: hidden;
  position: relative;
  display: block;
  padding-top: 59%;
}

.aspectRatio-picture-inside {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

h1.DefaultTitle {
  text-rendering: optimizeSpeed;
  font-size: 26px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 1.1;
  margin-bottom: 30px;
}

.DefaultSubtitle {
  font-size: 1.2rem;
  font-weight: 400;
  letter-spacing: inherit;
  line-height: 1.4rem;
}

@media (min-width: 768px) {
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 1000px) {
  .DefaultSubtitle {
    margin-bottom: 15px;
  }
}

@media (min-width: 601px) {
  h1.DefaultTitle {
    font-size: 41px;
    line-height: 1;
  }
}

@media (max-width: 678px) {
  .aspectRatio-picture.cls-mr {
    margin-right: -15px;
    margin-left: -15px;
  }
}

/*! CSS Used from: http://cardioactive4mexico/css/css__ZN93D8PfrRgsRXf4ZYn_Xk5s14HuG8y9aejptAkD164__A3Rhmg71HIy48lul9sDwTjsZMYy3vcqDMxiiGDpJ07A__xcNd_FrbI_VIZkttonrfJFKfCEe9bSH5yAFj4pQDNwU.css ; media=all */
@media all {
  * {
    margin: 0;
    padding: 0;
    outline: 0;
  }

  ul li {
    list-style-type: none;
  }

  img {
    -ms-filter: saturate(1.15);
    -webkit-filter: saturate(1.15);
    filter: saturate(1.15);
  }

  *::selection {
    background-color: #eee;
  }

  *::-moz-selection {
    background-color: #eee;
  }

  .contenido-principal {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  a {
    color: #005383;
    text-decoration: none;
  }

  a:hover {
    cursor: pointer;
  }

  li {
    list-style-type: none;
  }

  .panel-panel {
    max-width: 1200px;
    padding: 40px 0;
  }

  .panel-panel>div:empty {
    display: none;
    height: 0;
    margin: 0;
    padding: 0;
    width: 0;
  }

  .gl-Grid_12 {
    -ms-grid-column-span: 12;
    grid-column: span 12;
  }

  @media screen and (max-width:1100px) {

    .gl-Grid_12 {
      padding: 0 10px;
      width: auto;
    }

    .contenido-principal {
      margin-top: 1px;
    }
  }
}

.another {
  margin-top: 0;
  font-style: italic;
  padding: 5px;
}

.vospominan {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

@media (max-width:1200px) {
  .vospominan {
    max-width: 500px;
}
}

@media (max-width:600px) {
  .vospominan {
    width: 400px;
  }
}

@media (max-width:480px) {
  .vospominan {
    width: 300px;
  }
}

.indentity {
  font-size: 18px;
  font-weight: 600;
  margin: 0 !important
}

figure.testimonial {
  position: relative;
  float: left;
  overflow: hidden;
  margin: 10px 1%;
  padding: 0 20px;
  text-align: left;
  box-shadow: none !important;
}

figure.testimonial * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.35s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  transition: all 0.35s cubic-bezier(0.25, 0.5, 0.5, 0.9);
}

figure.testimonial img {
  max-width: 100%;
  vertical-align: middle;
  height: 90px;
  width: 90px;
  border-radius: 50%;
  margin: 40px 0 0 10px;
}

figure.testimonial blockquote {
  background-color: #fff;
  display: block;
  font-size: 18px;
  color: #000;
  font-weight: 400;
  line-height: 1.5em;
  margin: 0;
  padding: 25px 50px 30px;
  position: relative;
}

figure.testimonial blockquote:before,
figure.testimonial blockquote:after {
  content: "\201C";
  position: absolute;
  color: #ff5057;
  font-size: 50px;
  font-style: normal;
}

figure.testimonial blockquote:before {
  top: 25px;
  left: 20px;
}

figure.testimonial blockquote:after {
  content: "\201D";
  right: 20px;
  bottom: 0;
}

figure.testimonial .btn {
  top: 100%;
  width: 0;
  height: 0;
  border-left: 0 solid transparent;
  border-right: 25px solid transparent;
  border-top: 25px solid #fff;
  margin: 0;
  position: absolute;
}

figure.testimonial .peopl {
  position: absolute;
  bottom: 45px;
  padding: 0 10px 0 120px;
  margin: 0;
  color: #ffffff;
  -webkit-transform: translateY(50%);
  transform: translateY(50%);
}

figure.testimonial .peopl h3 {
  opacity: 0.9;
  margin: 0;
}

.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}

.slick-list {
  position: relative;
  display: block;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.slick-list:focus {
  outline: none;
}

.slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
  transform: translate3d(0, 0, 0);
}

.slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
}

.slick-track:before,
.slick-track:after {
  display: table;
  content: '';
}

.slick-track:after {
  clear: both;
}

.slick-loading .slick-track {
  visibility: hidden;
}

.slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.slick-slide img {
  display: block;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block;
}

.slick-loading .slick-slide {
  visibility: hidden;
}

.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}

.slick-btn.slick-hidden {
  display: none;
}

.slick-prev,
.slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 40%;
  display: block;
  width: 20px;
  height: 20px;
  padding: 0;
  transform: translate(0, -50%);
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.slick-prev:hover,
.slick-prev:focus,
.slick-next:hover,
.slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
}

.slick-prev:hover:before,
.slick-prev:focus:before,
.slick-next:hover:before,
.slick-next:focus:before {
  opacity: 1;
}

.slick-prev:before,
.slick-next:before {
  font-family: "FontAwesome";
  font-size: 40px;
  line-height: 1;
  opacity: .75;
  color: white;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.slick-prev {
  left: -40px;
}

.slick-prev:before {
  content: "";
}

.slick-next {
  right: -40px;
}

.slick-next:before {
  content: "";
}

.row-y-name, .name {
  font-size: 20px;
  color: #000;
}


.p-block-col__title {
  position: relative;
  margin: 0;
  margin-bottom: 20px;
  font-size: 26px;
  color: rgb(75, 79, 84);
}

.p-block-col__title::before {
  content: "";
  position: absolute;
  left: 0;
  top: calc(100% + 2px);
  width: 100%;
  height: 2px;
  background-color: rgb(1, 178, 170);
}

.list-reset {
  list-style: none;
  margin: 0;
  padding: 0;
}

.p-block-col__list {
  display: flex;
  justify-content: space-between;
}

.p-block-col__list--column {
  flex-direction: column;
}

.p-block-col__item {
  display: flex;
  flex-direction: column;
  width: calc(50% - 20px);
}

.p-block-col__item--row {
  flex-direction: row;
  align-items: center;
  width: auto;
}

.p-block-col__item--row:not(:last-child) {
  margin-bottom: 20px;
}

.p-block-col__heading {
  position: relative;
  align-self: flex-start;
  margin: 0 0 30px;
  font-size: 22px;
  color: rgb(75, 79, 84);
}

.p-block-col__heading::before {
  content: "";
  position: absolute;
  left: 0;
  top: calc(100% + 2px);
  width: calc(100% + 40px);
  height: 2px;
  background-color: rgb(224, 62, 82);
}

.p-block-col__heading--min {
  flex: 0 0 145px;
  align-self: center;
  margin-right: 10px;
}

.p-block-col__heading--min::before {
  width: 95%;
}

.p-block-col__icon {
  flex: 0 0 45px;
  margin-right: 20px;
  width: 45px;
  max-height: 60px;
}

.p-block-col__wrapper {
  display: flex;
}

.p-block-col__content {
  position: relative;
  flex-grow: 1;
  margin: 0;
  font-size: 20px;
}

@media screen and (max-width: 1000px) {
  .p-block-col__list {
    flex-direction: column;
  }

  .p-block-col__wrapper {
    flex-wrap: wrap;
  }

  .p-block-col__icon {
    flex-basis: 70px;
    width: 70px;
    max-height: 90px;
  }

  .p-block-col__item {
    width: 100%;
  }

  .p-block-col__item:not(:last-child) {
    margin-bottom: 40px;
  }
}
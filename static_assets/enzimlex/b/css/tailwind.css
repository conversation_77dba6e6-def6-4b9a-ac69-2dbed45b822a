/* @tailwind base; */

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-right: 2rem;
    padding-left: 2rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.-order-1 {
  order: -1;
}

.clear-both {
  clear: both;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mr-4 {
  margin-right: 1rem;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.flex {
  display: flex;
}

.hidden {
  display: none;
}

.h-24 {
  height: 6rem;
}

.w-full {
  width: 100%;
}

.w-24 {
  width: 6rem;
}

.max-w-\[80\%\] {
  max-width: 80%;
}

.max-w-\[320px\] {
  max-width: 320px;
}

.flex-grow {
  flex-grow: 1;
}

.basis-2\/3 {
  flex-basis: 66.666667%;
}

.basis-1\/3 {
  flex-basis: 33.333333%;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.gap-4 {
  gap: 1rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.border {
  border-width: 1px;
}

.bg-\[\#075AA8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(7 90 168 / var(--tw-bg-opacity));
}

.\!py-8 {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.font-bold {
  font-weight: 700;
}

.not-italic {
  font-style: normal;
}

.tracking-tighter {
  letter-spacing: -0.05em;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

html {
  scroll-behavior: smooth;
}

body {
  min-width: 360px;
  font-size: 18px;
}

img {
  display: block;
  max-width: 100%;
}

h1,
#main h2,
#main h3 {
  font-family: 'krona_oneregular', sans-serif;
}

.main {
  margin-bottom: 3rem;
}

.hl-accent {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity));
}

.hl-second {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(254 94 0 / var(--tw-text-opacity));
}

.why-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  padding: 0px;
}

.why-list > li {
  display: block;
}

.why-list > li > p {
  margin-bottom: 0px;
  display: inline-block;
  border-radius: 0.375rem;
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: #1d4ed8;
  --tw-gradient-to: rgb(29 78 216 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #38bdf8;
  background-attachment: fixed;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.symtoms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding: 0px;
}

.symtoms-list > li {
  display: flex;
  flex-grow: 1;
  flex-basis: calc(50% - 1rem);
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

@media (min-width: 640px) {
  .symtoms-list > li {
    flex-direction: row;
    text-align: left;
  }

  .symtoms-list > li:nth-child(even) {
    flex-direction: row-reverse;
    text-align: right;
  }
}

.symtoms-list > li > img {
  height: 6rem;
  width: 6rem;
  flex-shrink: 0;
  border-radius: 9999px;
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.actions-list {
  position: relative;
  list-style-type: none;
  overflow: hidden;
  border-radius: 0.375rem;
  padding: 1rem;
  padding-top: 3rem;
  padding-bottom: 3rem;
  background: url("https://assets.mirus.help/enzimlex/b/images/med-bg.jpg") no-repeat center / cover;
}

.actions-list::before {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 100%;
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
  --tw-gradient-from: rgb(3 105 161 / 0.5);
  --tw-gradient-to: rgb(3 105 161 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(30 64 175 / 0.5);
}

.actions-list > li {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  border-radius: 0.375rem;
  background-color: rgb(255 255 255 / 0.8);
  padding: 0.5rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.actions-list > li:before {
  content: '';
  background: url("https://assets.mirus.help/enzimlex/b/images/checkmark.svg") no-repeat left center / contain;
  margin-right: 0.5rem;
  display: inline-block;
  height: 2.5rem;
  width: 2.5rem;
  flex-shrink: 0;
}

.actions-list > li:not(:last-child) {
  margin-bottom: 2rem;
}

/* comments */

#comments.comments {
  margin-top: 1.5rem;
  margin-bottom: 3rem;
}

#comments .comments__title {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  text-transform: uppercase;
}

#comments .comments__list {
  margin-left: 0px;
  margin-bottom: 1.5rem;
}

#comments .comment {
  padding-left: 0px;
}

#comments .comment::before {
  --tw-content: none;
  content: var(--tw-content);
}

#comments .comment:not(:last-child) {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #ebebeb;
}

#comments .comment__wrap {
  position: relative;
  margin-right: 1rem;
}

#comments .comment__top {
  display: flex;
  align-items: center;
}

#comments .comment__img {
  height: 4rem;
  width: 4rem;
  flex-shrink: 0;
  border-radius: 9999px;
}

#comments .comment__title {
  margin-bottom: 0.25rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 700;
  line-height: 1;
}

#comments .comment__date {
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity));
}

#comments .comment__stars {
  position: absolute;
  left: 0;
  top: calc(100% + 8px);
  display: flex;
  justify-content: center;
  width: 100%;
  font-size: 0;
}

#comments .comment__stars > svg {
  display: block;
  height: 0.75rem;
  width: 0.75rem;
  color: #d80e2d;
  fill: currentColor;
}

#comments .comment__content {
  margin-left: 5rem;
}

#comments .comment__content > p {
  padding-bottom: 0px;
  font-size: 1rem;
  line-height: 1.5rem;
}

#comments .comment__content a {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(225 29 72 / var(--tw-text-opacity));
}

#comments .comment__content > img {
  display: block;
  max-height: 15rem;
  max-width: 100%;
  border-radius: 0.375rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

#comments .comment__content > p + img {
  margin-top: 0.5rem;
}

#comments .comment__content > p:first-child:first-letter {
  color: inherit;
  float: none;
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
  padding-top: inherit;
  padding-right: inherit;
  padding-left: inherit;
}

#comments .comments__btn {
  /* font-family: PTSans-Bold,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif; */
  font-weight: 700;
  font-size: 20px;
  line-height: 1.2;
  text-transform: uppercase;
  text-decoration: none;
  color: #fff;
  background: #255aa8;
  border: 1px solid #255aa8;
  border-radius: 3px;
  padding: 8px 16px;
  margin-top: 20px;
  display: inline-block;
  transition: all .35s ease-in-out;
}

#comments .comments__btn:hover,
#comments .comments__btn:focus {
  color: #255aa8;
  background: #fff;
}

.swiper {
}

.swiper img {
  margin-left: auto;
  margin-right: auto;
}

.swiper-wrapper {
  transition-timing-function: linear !important;
}

@media (min-width: 640px) {
  .sm\:-order-none {
    order: 0;
  }

  .sm\:float-left {
    float: left;
  }

  .sm\:mr-2 {
    margin-right: 0.5rem;
  }

  .sm\:mr-4 {
    margin-right: 1rem;
  }

  .sm\:flex-nowrap {
    flex-wrap: nowrap;
  }
}

@media (min-width: 768px) {
  .md\:max-w-\[80\%\] {
    max-width: 80%;
  }
}
<!DOCTYPE html><html lang="es" dir="ltr"><head><link rel="preconnect" href="//rocket-commander-prod.b-cdn.net/">

	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Blog Saúde Melhor Idade</title>
	<link rel="stylesheet" href="css/kuN2yz08L5pD.css">
	<link rel="icon" href="images/XEU0tX32y5hw.ico" type="image/x-icon">

</head>

<body class="page-template-default page page-id-9795 disable-right-click" >

	<article class="container " role="main">

		<p class="adv"><small>Noticias mas vistas</small></p>
		<header class="header">
			<h1>MInvestigadora Desarrolla "Aceite de Ostra Africana" Que Promueve Erecciones Instantáneas de Hasta 4
				Horas Sin Efectos Secundarios
			</h1>
			<h2><em><i>"Si no fuera por esto, habría engañado a mi esposo." - dijo la especialista sobre este nuevo
						tratamiento, que se puede realizar en casa y acaba de ser
						aprobado por Invima en Colombia.</i></em></h2><i>
			</i>
		</header>
		<div class="stars" data-stars="4">
			<svg class="star rating" data-rating="1">
				<polygon points="9.9, 1.1, 3.3, 20, 19.8, 8.58, 0, 8.58, 16.5, 20" class="inL_909744"></polygon>
				
			</svg><svg class="star rating" data-rating="2">
				<polygon points="9.9, 1.1, 3.3, 20, 19.8, 8.58, 0, 8.58, 16.5, 20" class="inL_738097"></polygon>
				
			</svg>
			<svg class="star rating" data-rating="3">
				<polygon points="9.9, 1.1, 3.3, 20, 19.8, 8.58, 0, 8.58, 16.5, 20" class="inL_875107"></polygon>
				
			</svg><svg class="star rating" data-rating="4">
				<polygon points="9.9, 1.1, 3.3, 20, 19.8, 8.58, 0, 8.58, 16.5, 20" class="inL_304773"></polygon>
				
			</svg><svg class="star rating" data-rating="5">
				<polygon points="9.9, 1.1, 3.3, 20, 19.8, 8.58, 0, 8.58, 16.5, 20" class="inL_982091"></polygon>
				
			</svg> <span>(2234 Deseos)</span>
		</div>
		<div class="the-content">
			<figure class="wp-block-image">
				<a href="#toform">
					<picture>
						<source srcset="images/XVRRFMYCYSmT.webp" type="image/webp">
						<img src="images/AY1EHhPNqP3r.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>


			<p class="inL_542871"><strong>No es ninguna novedad que muchos hombres,
					principalmente los mayores de 40 años, se frustran en el
					momento H debido a que su amigo de abajo no colabora...</strong></p>
			<p class="inL_97333">Solo aquellos que han pasado por eso saben lo
				embarazoso y vergonzoso que es tener a una mujer dispuesta a hacer
				locuras entre cuatro paredes...</p>
			<p class="inL_784338">Y no ser capaz de satisfacerla y tener una erección
				decente.</p>
			<p class="inL_22916">Después de ver a su esposo Carlos sufrir de
				impotencia durante años y casi tener un ataque cardíaco por el abuso
				del "pastilla azul"...</p>
			<p class="inL_974461">La colombiana Carmen Macedo, con doctorado en salud
				sexual masculina de la Universidad de Harvard, desarrolló un
				<strong>"aceite de ostra africana"</strong> con el poder de revertir la disfunción eréctil...
			</p>
			<p class="inL_830454">Y hacer que cualquier hombre, <strong>de cualquier
					edad, pueda volver a tener erecciones firmes y
					duraderas</strong> casi cuando quiera, y así recuperar su virilidad.</p>



			<figure class="wp-block-image inL_670791">
				<a href="#toform">
					<picture>
						<source srcset="images/9VDkHU0PfY0T.webp" type="image/webp">
						<img src="images/Km5NKxV9xcxw.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>


			<p class="inL_726687">Lo más impresionante es que conseguirás todo esto
				sin necesidad de medicamentos peligrosos (que pueden causarte
				un ataque cardíaco)...</p>
			<p class="inL_869187">Tampoco necesitarás usar bombas de pene o realizar
				cirugías riesgosas, que son muy costosas y pueden dejarte
				sexualmente discapacitado.</p>
			<p class="inL_796918">Esto se debe a que lo que hace este aceite de ostra
				africana es actuar en la <strong>verdadera causa de la
					disfunción eréctil</strong>, algo que los medios tradicionales o tu médico nunca te contaron…
			</p>
			<p class="inL_904968">Un <strong>"parásito silencioso"</strong> que está
				infectando tu sistema reproductor masculino, bloqueando tu
				capacidad para tener las erecciones que te gustaría y destruyendo tu vida sexual.</p>
			<p class="inL_788683">Continúa leyendo este artículo, porque en las
				próximas líneas vamos a revelar en exclusiva:</p>
			<p class="inL_715246">• El curioso hecho de que esta
				extraña especie de "ostra africana" sea la clave que permitió
				este innovador descubrimiento en el campo de la salud sexual masculina…</p>
			<p class="inL_469204">• El verdadero villano de las
				erecciones: lo que REALMENTE está destruyendo tus erecciones
				con el paso de los años (<strong>Pista</strong>: no tiene nada que ver con tu edad, ni con la
				disminución de tu testosterona);</p>
			<p class="inL_108482">• Y también cómo este
				<strong>nuevo y clínicamente probado aceite de ostra africana</strong>,
				que se puede usar en casa y ya ha sido validado por más de 120.500 hombres en Colombiay en todo el
				mundo, puede revertir tu disfunción eréctil, devolver la
				potencia de tus erecciones y recuperar tu masculinidad.</p>
			<p class="inL_837235">
				Tendrás la Capacidad de Satisfacer los Deseos más Ardientes
				de una
				Mujer de Nuevo.</p>



			<figure class="wp-block-image inL_327000">
				<a href="#toform">
					<picture>
						<source srcset="images/cD8QURw74lIx.webp" type="image/webp">
						<img src="images/uUZ3KeY4RUcd.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>


			<p class="inL_361879">Gracias a la naturaleza afrodisíaca de esta rara
				ostra africana, tendrás resultados <strong>rápidos y
					garantizados</strong> como los de Carlos…</p>
			<p class="inL_15236">Olvidándote de todos aquellos momentos en que tu
				"miembro" simplemente no se levantaba, sin importar lo que tú o
				tu pareja hicieran…</p>
			<p class="inL_657973">Después de consumir este aceite de ostra, podrás no
				solo recuperar tus erecciones, sino también aumentar tu
				vigor, libido y disposición.</p>
			<p class="inL_717163">Es la mejor alternativa de tratamiento, porque:</p>
			<p class="inL_860568">1. Te permite tener erecciones instantáneas, firmes y
				duraderas justo después del uso (hasta 4 horas de duración);
			</p>
			<p class="inL_654647">2. Es fácil y práctico de usar: todos pueden hacer el
				tratamiento por su cuenta en casa y de manera discreta, sin
				necesidad de consultas médicas;</p>
			<p class="inL_359970">3. Es 100% natural, saludable y sin efectos
				secundarios, especialmente cuando se trata de la salud del corazón;
			</p>
			<p class="inL_52613">4. Te permite ahorrar dinero en lugar de gastarlo en
				medicamentos llenos de químicos y terapias costosas e
				ineficaces.</p>
			<p class="inL_677782">Ya no tendrás que preocuparte por fallar en el
				momento del sexo, y volverás a tener esas erecciones firmes,
				duraderas e instantáneas que tenías cuando eras joven…</p>
			<p class="inL_614089">Y dejarás a cualquier mujer a tus pies, rogándote
				por más...</p>
			<p class="inL_859947">Y todo esto gracias al descubrimiento de este aceite
				de ostra africana por la Dra. Carmen, que garantiza las
				erecciones firmes que naciste para tener.</p>
			<p class="inL_688544">¿Cómo lo hizo? Lo explicaremos:</p>
			<p class="inL_185252">
				Ella Quería Evitar una Infidelidad y Salvar su Matrimonio
			</p>




			<figure class="wp-block-image inL_514714"><a href="#toform"><img src="images/TSffEBVibtK1.jpg" alt="" class="lazyload" loading="lazy"></a>
			</figure>

			<p class="inL_836129">Según un estudio del Ministerio de Salud, 7 de cada
				10 hombres en Colombia, de entre 30 y 90 años, sufren de
				disfunción eréctil.</p>
			<p class="inL_149888">Y lo que es más aterrador: de cada 6 hombres mayores
				de 40 años, la probabilidad de <strong>morir a causa de un
					ataque cardíaco</strong> debido al uso constante del "azulzinho" es de 1 en 6.</p>
			<p class="inL_109155">Es decir, es como si estuvieras jugando a la ruleta
				rusa con un revólver de 6 tiros, cada vez que tomas un
				"azulzinho" para intentar tener una erección satisfactoria…</p>
			<p class="inL_580187">Lo peor es que la mayoría de las veces, los hombres
				no buscan un tratamiento adecuado y aceptan que esto es algo
				normal, que sucede debido a la edad...</p>
			<p class="inL_984012"><strong>Pero el problema parece ser mucho más serio
					de lo que parece…</strong></p>
			<p class="inL_971664">Además del riesgo cardíaco, el medicamento también
				causa dependencia: una vez dependiente, es muy difícil dejar
				de tomarlo.</p>
			<p class="inL_583393">Y todos sabemos lo <strong>horrible</strong> que es
				depender del "azulzinho"!</p>
			<p class="inL_649289">Eso fue precisamente lo que le sucedió al marido de
				Carmen, Carlos:</p>
			<p class="inL_680990">"Al principio, no se levantaba de vez en cuando…</p>
			<p class="inL_685606">Con el tiempo, se convirtió en rutina…</p>
			<p class="inL_869498">Hasta llegar a la catastrófica situación de que no
				se levantaba en ningún momento…</p>
			<p class="inL_507631">Además del "azulzinho", Carlos llegó a comprar esas
				<strong>cápsulas</strong> que venden en Internet para ver si
				al menos lograba una erección semi firme, pero fue completamente inútil…</p>
			<p class="inL_403056">Todo esto trajo graves consecuencias, tanto para su
				salud como para nuestro matrimonio, que estaba completamente
				arruinado…</p>
			<p class="inL_778279">Gastaba una fortuna en lencería para seducirle, pero
				nada funcionaba…</p>
			<p class="inL_149920"><strong>¡No se levantaba de ninguna manera!</strong>
			</p>
			<p class="inL_381125">Eso me hizo sospechar que ya no me amaba, incluso
				llegué a pensar que me estaba engañando con alguien...</p>
			<p class="inL_450499">Un tiempo después, debido a los efectos secundarios
				del viagra, dejó de usarlo y renunció a tener relaciones
				sexuales conmigo.</p>
			<p class="inL_379242">Pasamos meses sin tener sexo y, como mi marido no
				estaba cumpliendo con su papel de hombre en la relación,
				comencé a sentirme desatendida, y confieso que llegué a <strong>considerar engañarlo con un
					compañero de trabajo...</strong></p>
			<p class="inL_415558">Hasta el día de hoy no me he perdonado por pensar de
				esa manera, pero tenía mis necesidades, ¿sabes?</p>
			<p class="inL_169373"><strong>Le sucede a cualquier mujer: si no estás
					satisfaciendo a tu esposa como deberías, puedes estar seguro de
					que buscará satisfacerse en otro lugar.</strong></p>
			<p class="inL_710628">¡Y eso parecía ser el golpe final en nuestro
				matrimonio! El hombre fuerte del que me enamoré, que me hacía tener
				múltiples orgasmos, ya no podía cumplir su<strong> papel de hombre</strong> y estaba deprimido."</p>




			<figure class="wp-block-image inL_385425">
				<a href="#toform">
					<picture>
						<source srcset="images/6NFtqX6PxJe2.webp" type="image/webp">
						<img src="images/wHtaKkIthiap.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>

			<p class="inL_4148">Al borde de cometer una infidelidad, Carmen decidió
				calmarse y buscar soluciones en su área profesional para
				salvar su matrimonio…</p>
			<p class="inL_604891">Y comenzó a investigar incansablemente sobre la
				verdadera causa de la disfunción eréctil, responsable de la
				epidemia de hombres "castrados" que tenemos en Colombia actualmente.</p>





			<figure class="wp-block-image inL_964623">
				<a href="#toform">
					<picture>
						<source srcset="images/W5Zo2OLmpJqP.webp" type="image/webp">
						<img src="images/qt9drVaOuAT7.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>


			<p class="inL_16111">"Estaba harta de sufrir con todo esto, y para evitar
				a toda costa engañar a mi esposo, busqué una solución que
				resolviera la disfunción eréctil de Carlos de raíz.</p>
			<p class="inL_666789">Probé varias sustancias en el laboratorio.
				<strong>Todas eran 100% naturales y seguras para el cuerpo.</strong>
			</p>
			<p class="inL_467979">Pero el éxito llegó cuando separé algunos
				ingredientes africanos poco conocidos y los combiné con activos que ya
				sabemos que funcionan para el hombre, creando entonces una <strong>fórmula nano-molecular
					única.</strong></p>
			<p class="inL_228248">Incluso, su <strong>eficiencia del 98%</strong> fue
				confirmada por los centros de investigación más importantes
				de Europa y Estados Unidos!"</p>
			<p class="inL_298933">Incluso durante las pruebas, su fórmula
				nano-molecular promovía <strong>erecciones prácticamente
					instantáneas.</strong></p>
			<p class="inL_666317">"Carlos logró tener su primera erección después de
				meses sin tenerla, justo después de aplicar esta fórmula
				innovadora. Se alegró porque no tuvo ningún efecto secundario. Y eso fue sólo el principio.</p>
			<p class="inL_274519">Tuvimos relaciones sexuales todos los días después
				de que él aplicó la fórmula, y no falló en ninguna de
				ellas...</p>
			<p class="inL_73973">Incluso, su rendimiento era irreconocible,
				<strong>duraba horas en la cama y me estaba volviendo loca!"</strong>
			</p>
			<p class="inL_815031">
				¿Cómo funciona esta fórmula?</p>
			<p class="inL_750039">Lo que ninguno de nosotros sabía era que Carlos era
				sólo uno de los millones de hombres alrededor del mundo que
				estaba siendo castrado químicamente todos los días por un <strong>parásito silencioso.</strong></p>
			<p class="inL_263554">Algo que ataca directamente su sistema hormonal,
				aumentando sus niveles de estrógeno y bloqueando la producción
				de testosterona.</p>
			<p class="inL_917632">Primero debes entender que la testosterona, es
				decir, la hormona masculina, es la principal responsable de tu
				libido, además de avisar a tu cerebro para bombear sangre hacia tu cuerpo cavernoso peneano, y hacer
				que tengas una erección.</p>




			<figure class="wp-block-image inL_818605"><a href="#toform"><img src="images/uSU17BQdmehV.jpg" alt="" class="lazyload" loading="lazy"></a>
			</figure>


			<p class="inL_383780">Normalmente, los hombres son informados por los
				médicos tradicionales que necesitan aumentar su testosterona,
				porque esta naturalmente disminuye a medida que envejecen…</p>
			<p class="inL_596767">Pero entiende que esto es <strong>ERRÓNEO</strong>,
				tu testosterona NO disminuye lo suficiente como para dejarte
				impotente, y ya vamos a revelar en las próximas líneas cuál es el verdadero culpable de tu
				disfunción eréctil.</p>
			<p class="inL_147070">Además, tenemos la hormona femenina llamada
				estrógeno, también presente en los hombres, pero en cantidad muy
				pequeña, cuya función es alertar a tu cerebro de que no es el momento para que tengas impulsos
				sexuales, es decir, una erección…</p>
			<p class="inL_970101">Y es por eso que necesitamos de él, o de lo
				contrario, nosotros los hombres estaríamos con la tienda armada 24
				horas al día, debido a nuestra brutal cantidad de testosterona…</p>
			<p class="inL_570869">Hasta aquí todo bien, muy sencillo de entender
				¿verdad?</p>
			<p class="inL_720028">El problema es que durante toda tu vida, has
				consumido alimentos en envases plásticos y con una serie de
				conservantes químicos…</p>
			<p class="inL_765802">¿Y qué tiene que ver eso con tu erección?<strong>
					Absolutamente TODO.</strong></p>
			<p class="inL_546780">Lo que ocurre es que tanto en el plástico como en
				los conservantes químicos existe un parásito silencioso e
				invisible llamado <strong>xenoestrógeno…</strong></p>
			<p class="inL_841517">Recibe este nombre debido a su altísima similitud
				molecular con el estrógeno, que es la hormona femenina…</p>
			<p class="inL_273775">Lo peor de todo es que estos productos químicos no
				solo son indetectables… ¡sino que están EN TODAS PARTES!
				Latas de refrescos, cerveza, champú, jabón e incluso en la puerta de tu refrigerador!</p>
			<p class="inL_282232">Así, después de algunos años expuesto a estas
				sustancias, tu cuerpo va almacenando una gran cantidad de
				xenoestrógeno.</p>
			<p class="inL_268518">¿Y lo peor de todo esto? Es que este parásito tóxico
				tiene el <strong>poder de transformar tu testosterona en
					estrógeno…</strong></p>
			<p class="inL_708009">Es decir: tu cerebro se ve completamente impedido de
				enviar señales para que la sangre sea bombeada a tu pene,
				lo que bloquea tu erección.</p>
			<p class="inL_85350"><strong>Es precisamente por eso que tus erecciones
					se vuelven más débiles cuando envejeces: no porque estás más
					viejo, sino porque has acumulado altos niveles de xenoestrógeno en el cuerpo a lo largo de los
					años!</strong></p>
			<p class="inL_611084">Después de todo esto, debes estar preguntándote…</p>
			<p class="inL_158748">
				¿Cómo Eliminar y Bloquear el Xenoestrógeno?</p>


			<figure class="wp-block-image inL_831449"><a href="#toform"><img src="images/kzpoyi5ar0tw.jpg" alt="" class="lazyload" loading="lazy"></a>
			</figure>


			<p class="inL_123569">Como especialista reconocida en salud sexual
				masculina, a Carmen se le enseñó a ver el sistema reproductor
				masculino como si fuera una máquina perfecta…</p>
			<p class="inL_328054">Cada pieza de esta máquina trabaja en conjunto para
				que pueda alcanzar su máximo rendimiento…</p>
			<p class="inL_520190">Y cuando una pieza no está funcionando, no sirve de
				nada remendarla e intentar soluciones paliativas…</p>
			<p class="inL_530154">¡Debes RESOLVER el origen del problema!</p>
			<p class="inL_395472">Y eso es exactamente lo que hacen las drogas
				farmacéuticas: proporcionan una solución artificial a tu problema,
				pero él no deja de existir…</p>
			<p class="inL_742902">Es como si supieras que tu casa está SUCIA y
				continúas barriendo la basura bajo la alfombra.</p>
			<p class="inL_215329">¿El resultado? Disfunción eréctil, dependencia
				química a medicamentos, o incluso la muerte por infarto…</p>
			<p class="inL_283726">Entonces, en lugar de solo remendar la pieza de esta
				máquina, <strong>debes reparar lo que está causando su mal
					funcionamiento</strong>, es decir, erradicar el mal de raíz…</p>
			<p class="inL_428111">En otras palabras, <strong>maximizar tu
					testosterona</strong>, apoyar la creación de nueva testosterona y
				aumentar la producción de andrógenos (El andrógeno es el precursor de la testosterona).</p>
			<p class="inL_735124">Y al mismo tiempo, bloquear el ataque diario de
				todos esos venenos químicos, como el xenoestrógeno.</p>
			<p class="inL_572464">Entonces, cuando Carmen miró el sistema reproductor
				masculino como una MÁQUINA, y después de algunos miles de
				dólares invertidos en investigación en la Universidad de Harvard...</p>
			<p class="inL_486996">Finalmente pudo encontrar la fórmula perfecta que le
				dio mejores resultados de lo que ella esperaba que fuera
				posible.</p>
			<p class="inL_912440">Ahora, probablemente quieras saber…</p>
			<p class="inL_215770">
				¿Cuál sería exactamente esa fórmula?</p>
			<p class="inL_457541">El primer y principal activo es el aceite de la
				<strong>ostra llamada Ostreidae Africana.</strong></p>


			<figure class="wp-block-image inL_400330">
				<a href="#toform">
					<picture>
						<source srcset="images/xFvsWgbP7jYy.webp" type="image/webp">
						<img src="images/Fi6AqS0W4oV8.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>




			<p class="inL_775256">Esta ostra se encuentra desde la ciudad de Luderitz,
				en Namibia, y se extiende por toda la costa africana.</p>
			<p class="inL_615116">Mira: todo hombre sabe que la ostra posee un alto
				poder afrodisíaco…</p>
			<p class="inL_614366">De hecho, la revista científica <strong>The
					Lancet</strong> afirmó en una investigación que la ostra común tiene
				el poder de aumentar el poder de las erecciones en casi un 30%.</p>
			<p class="inL_6500">Pero esta no era una ostra común…</p>
			<p class="inL_912725">Encontrada solo en la costa de Namibia, en África,
				esta ostra africana es utilizada por la tribu indígena Himba
				desde hace siglos como alimento para aumentar la disposición.</p>
			<p class="inL_770280">De hecho, esta ostra fue descubierta casi por
				accidente cuando los científicos descubrieron que los hombres
				mayores de 40 que viven en esta región de la costa africana, tenían un índice de <strong>solo un 4%
					de disfunción eréctil, un número ridículo, comparado con
					casi el 90% del promedio mundial.</strong></p>
			<p class="inL_263039">Al investigar la razón de este índice tan bajo de
				impotencia, al final de la investigación se reveló que,
				curiosamente, forma parte de la cultura de los hombres de la región consumir esta ostra desde la
				adolescencia como si fuera un alimento común, para aumentar la
				disposición.</p>




			<figure class="wp-block-image inL_260628">
				<a href="#toform">
					<picture>
						<source srcset="images/JV0kOUDcQy7C.webp" type="image/webp">
						<img src="images/Ag2g1hYKYMim.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>


			<p class="inL_432604"><strong>Apenas sabían que, aquí en occidente, esto
					está siendo considerado el elemento natural más potente para
					mantener la virilidad del hombre adulto en la cama durante horas, aumentando la libido y la
					potencia de las erecciones en casi un 250%, ¡mucho más que la
					ostra común!</strong></p>
			<p class="inL_403341">En segundo lugar, tenemos la hierba <strong>coffea
					robusta</strong>. También encontrada en suelo africano, este
				potente afrodisíaco natural tiene el poder de potenciar la libido y aumentar la disposición,
				principalmente en hombres mayores de 40 años.</p>
			<p class="inL_461456">En tercer lugar, tenemos la combinación de los
				aminoácidos <strong>Arginina, Carnitina y Taurina</strong>,
				responsables de aumentar el flujo sanguíneo hacia el pene, facilitando que tengas erecciones con más
				facilidad.</p>
			<p class="inL_19412">En cuarto lugar, la fórmula aún está compuesta por
				los raros extractos de <strong>Magnesio, Selenio, Zinc y
					Boro</strong>, que también actúan en el aumento de la circulación del pene, reforzando las
				erecciones y garantizando que tu "miembro" permanezca firme como
				una roca.</p>
			<p class="inL_731502">Por último, tenemos las vitaminas
				<strong>colecalciferol, ácido ascórbico, tocoferol, piridoxina y ácido
					fólico</strong>, que actúan como un escudo protector contra el xenoestrógeno y evitan que tus
				erecciones se vean bloqueadas nuevamente.</p>
			<p class="inL_904475">Y esto se pone aún mejor, porque…</p>
			<p class="inL_207713">
				Esta es la Solución Más Rápida y Fácil de Usar para
				Erecciones
				Garantizadas</p>
			<p class="inL_128465">Esta solución es diferente a todo lo que has visto
				antes, ya que se ha creado en formato de gotas que utiliza la
				<strong>tecnología SRV de aplicación sublingual para resultados inmediatos</strong>, haciendo que el
				efecto de los ingredientes sea <strong>185 veces</strong>
				mayor que en la forma tradicional.
			</p>
			<p class="inL_407567">Funciona así: como todos saben, la mejor forma
				posible de ingerir una sustancia medicinal, según la ciencia, es
				vía sublingual, es decir, debajo de la lengua.</p>
			<p class="inL_338623">Esto sucede debido a la gran cantidad de vasos
				sanguíneos presentes debajo de nuestra lengua, que absorben las
				sustancias naturales y rápidamente las transportan a nuestros órganos.</p>
			<p class="inL_771088">Cada dosis de 20 gotas (1 ml) de esta fórmula tiene
				un efecto de<strong> 4 horas</strong>, ¡así que solo
				necesita tomar una dosis al día para tener <strong>erecciones garantizadas en una noche de
					placer</strong> con su esposa!</p>
			<p class="inL_310373">Es importante saber también que no tendrá una
				erección si no hay algún estímulo en su cuerpo, así que puede
				estar tranquilo de que no se quedará con la tienda armada en caso de que tenga que salir de casa.
			</p>
			<p class="inL_767815">Simplemente toma 20 gotas debajo de la lengua hasta
				30 minutos antes de tener una relación. ¡Y también podrá,
				como Carlos y tantos otros, tener erecciones firmes y prolongadas cuando quiera!</p>
			<p class="inL_755237">¡No hay necesidad de medicamentos fuertes también!
			</p>
			<p class="inL_413304">Sin tener que usar cápsulas de harina completamente
				inútiles.</p>
			<p class="inL_732984">Sin cirugías caras y peligrosas.</p>
			<p class="inL_8045">¡Sentirás cada gota actuando en tu organismo!</p>
			<p class="inL_230579">Pero antes de todo…</p>
			<p class="inL_234977">
				¿Es seguro este "aceite de ostra africana"?</p>
			<p class="inL_199280">Como es un compuesto desarrollado en los mejores
				laboratorios del mundo, por una especialista de la Universidad
				de Harvard, el "aceite de ostra africana" es muy seguro.</p>
			<p class="inL_293207">Además, tiene la autorización de Invima y del
				Ministerio de Salud, sin ningún efecto secundario.</p>
			<p class="inL_828124">
				¿Cómo comienzas a usar el "aceite de ostra africana"?</p>
			<p class="inL_521501">¡Hoy es tu día de suerte!</p>
			<p class="inL_159848">La especialista Carmen finalmente consiguió la
				aprobación de su equipo e inversores para distribuir el "aceite
				de ostra africana" en Colombia.</p>
			<p class="inL_863267">Aquí, se está comercializando con el nombre de
				<strong>Virex.</strong></p>
			<p class="inL_671238">Cada frasco de Virex viene con 30 ml de
				aceite de ostra y los demás activos potenciadores de las
				erecciones, suficiente para 1 mes.</p>
			<p class="inL_78811">El equipo de Carmen invirtió millones de dólares en
				investigación y para desarrollar esta solución en gotas.</p>
			<p class="inL_91396">Normalmente, un frasco de un mes de esta solución
				para erecciones garantizadas costaría alrededor de
				<strong>$523327,00 pesos</strong>.
			</p>
			<p class="inL_118522">Pero ... eso no es lo que pagarás hoy por
				<strong>Virex.</strong></p>
			<p class="inL_253088">Mostraremos el precio con descuento en un segundo,
				pero primero…</p>
			<p class="inL_714857">
				Mira Lo Que la Gente Está Diciendo Sobre Virex</p>

			<figure class="wp-block-image inL_130824">
				<a href="#toform">
					<picture>
						<source srcset="images/pyuIyJRWkXE6.webp" type="image/webp">
						<img src="images/HewSYXXBTYOI.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>

			<p class="inL_349431">"Escuché sobre las pruebas exitosas de la Dra.
				Carmen, pero pensé que era demasiado bueno para ser verdad.
				Bueno... tomé el combo que más compensa, de 5 frascos, ¡y ni siquiera recuerdo la última vez que no
				pude rendir en la cama! Dios bendiga a todos los que prueben
				Virex." - Guillermo Zapata, Bogotá</p>

			<figure class="wp-block-image inL_71920">
				<a href="#toform">
					<picture>
						<source srcset="images/fq5jddMxarvY.webp" type="image/webp">
						<img src="images/EpNcSj2cGHMD.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>


			<p class="inL_297956">"Hacía 7 meses que no podía tener una erección.
				Ningún medicamento pudo ayudarme. Con el primer uso de Virex ya pude. Solo 20 gotitas debajo de la lengua antes de hacer el amor. ¡Tan fácil! ¡Gracias!" -
				Pedro Sanchez de Popayán</p>

			<figure class="wp-block-image inL_357312">
				<a href="#toform">
					<picture>
						<source srcset="images/GkcMEdRtAcTy.webp" type="image/webp">
						<img src="images/zYTgMdpZAceL.jpg" alt="" class="lazyload" loading="lazy">
					</picture>
				</a>
			</figure>

			<p class="inL_769785">“Me emocioné cuando mi amigo sugirió Virex...
				dijo que después de tomarlo estaba haciendo el amor todos
				los
				días con su mujer. Finalmente recuperé la confianza en la cama. Fue increíble."- Héctor Alonso,
				Medellin</p>


			<p class="inL_481012">
				El Equipo de la Dra. Carmen Ofrecerá 200 Botellas GRATIS de
				Virex y 2 Ventajas Exclusivas, Solo Para Nuestros Lectores</p>
			<p class="inL_518002">Para incentivar a los colombianos a tratar la
				disfunción eréctil de manera definitiva, el equipo de Virex
				ha logrado 2 ventajas exclusivas para los lectores de este reportaje que adquieran Virex hoy,
				a través del sitio oficial, incluyendo <strong>200</strong>
				unidades GRATIS.</p>
			<p class="inL_600115">Funciona así:</p>
			<p class="inL_832885">Si compras 2 botellas de Virex, obtienes una
				más<strong> completamente gratis.</strong></p>
			<p class="inL_794932">Y si compras 3 botellas... <strong>obtienes 2 más
					gratis y envío 100% Gratis!</strong></p>
			<p class="inL_641776"><strong>Son 5 meses de tratamiento para que tengas
					el mejor sexo posible..</strong>.</p>
			<p class="inL_76313">Sin preocuparte por no poder mantener la erección o
				durar poco tiempo, ¡sin la inseguridad de no poder
				satisfacer a tu pareja!</p>
			<p class="inL_718904"><strong>Ahora, otra ventaja exclusiva: todos los
					lectores que adquieran Virex hoy obtienen una GARANTÍA
					BLINDADA DE ERECCIONES.</strong></p>
			<p class="inL_648479">Funciona así: ve al enlace exclusivo de Virex
				a continuación y elige el tratamiento más vendido de 3
				botellas + 2 gratis, u otro que prefieras.</p>
			<p class="inL_195198">Usa 20 gotas (solo 1 ml) 30 minutos antes de tener
				una relación.</p>
			<p class="inL_2739">Y si no tienes una erección al usar Virex...
			</p>
			<p class="inL_270813">...simplemente envía un mensaje al correo
				electrónico <a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="ddaeb2adb2afa9b89d8bb4afb8a5f3beb2b0">[email protected]</a> dentro de los 90 días posteriores
				a
				la compra, citando la <strong>Garantía Incondicional de Erecciones</strong>, y el equipo de Virex te devolverá cada centavo que pagaste por el producto.
			</p>
			<p class="inL_495814"><strong>Es decir: no arriesgas absolutamente NADA y
					tienes 3 largos meses para probar Virex!</strong></p>
			<p class="inL_815553">Es decir, en el peor de los casos, si el tratamiento
				no funciona para ti, solo tienes que enviar un correo
				electrónico y recibirás todo tu dinero de vuelta el mismo día.</p>
			<p class="inL_725041">Cuando preguntamos a los distribuidores del producto
				en Colombiacómo podrían ofrecer una garantía tan poderosa,
				nos respondieron:</p>
			<p class="inL_605493">"Hay dos motivos: primero, sabemos que Virex
				realmente funciona y realmente te ayuda a vencer la
				disfunción. Tenemos un índice de satisfacción del 98,7%. Por eso, podemos ofrecer esta garantía.
				Después de todo, piénsalo: si no funcionara, nuestra empresa ya
				habría quebrado con la cantidad de reembolsos... ¿no es así?"</p>
			<p class="inL_548025">"En segundo lugar, sabemos que después de que
				pruebes Virex, lo recomendarás a tus amigos, a tu
				familia...
				y nosotros ganaremos muchos más clientes. Entonces, no tenemos nada que perder. Confiamos en nuestro
				producto, y estamos dispuestos a arriesgar nuestro propio
				dinero por ello", concluyeron.</p>
			<p class="inL_128455">Ahora, si aún no te has decidido... ¿Qué tienes que
				perder?</p>
			<p class="inL_899287">Tienes dos opciones de elección.</p>
			<p class="inL_668331">Elección Nº 1: Decides que Virex no es para
				ti y está todo bien...</p>
			<p class="inL_72560">...Entonces, puedes salir de esta página y continuar
				con tu vida de la misma manera que ahora.</p>
			<p class="inL_605145">Puedes seguir <strong>avergonzándote</strong> cada
				vez que vas a hacer el amor con una mujer, y sintiéndote
				<strong>menos hombre</strong> por no poder mantener la erección.
			</p>
			<p class="inL_211408">O, Elección Nº 2: Puedes transformarte en una
				máquina de amor, satisfacer a <strong>cualquier</strong> mujer en
				la cama y liberarte de la disfunción eréctil usando Virex...</p>
			<p class="inL_438706">...Y ver cómo es liberarte de una vez por todas de
				las posibilidades de no rendir en la cama.</p>




			<div class="inL_484041">

				<p class="inL_477071"><span class="inL_276303"> ¡Pero ATENCIÓN! Como el producto tiene materia
						prima importada y los inventarios aún se ven afectados por la guerra en Rusia, las ventas se
						terminarán el <span id="tempoHora" class="inL_51749">
							<strong>
								<a href="#toform" class="inL_66152"><span class="date-0"></span></a>
							</strong>
						</span>, sin una fecha estimada de regreso.
					</span></p>
			</div>
			<p class="inL_101697">Entonces, el momento de asegurar tu Virex es
				ahora.</p>
			<p class="inL_445547"><strong>No es mañana, ni la próxima
					semana...</strong></p>
			<p class="inL_422278">Si sales de esta página, o tardas demasiado en
				actuar, probablemente nunca más tendrás acceso a esta oferta
				nuevamente antes de que los inventarios vuelvan a los niveles normales.</p>

			<div id="toform"></div>
			<div class="form__wrapper">
				<img src="images/MfJR247eY7g2.png" alt="">
				<h3>¡Especialmente para ti!</h3>
				<h3>Apresurese a ordenar con descuento <span class="red">50%</span>!</h3>
				<div class="form__price">
					<div class="price__old">
						<span class="x_price_previous">258000</span>
						<span class="x_currency">cop</span>
					</div>  
					<div class="price__new">
						<span class="x_price_current">129000</span>
						<span class="x_currency">cop</span>
					</div>
				</div>
				<form class="form x_order_form lead-form" id="orderForm" method="post">
						   <input type="hidden" id="offer_id" name="offer_id" value="14264">
              <input type="hidden" id="country" name="country" value="CO">
             <input type="hidden" id="sub1" name="sub1" value="c">

			 <input class="input-roulette" id="name" name="name" placeholder="Mi nombre" required="" type="text" autocomplete="name">
			<input class="input-roulette only_number onlynumber" name="phone" id="phone" type="tel"placeholder="Número de teléfono" required="" autocomplete="tel">
					<!-- Добавляем контейнер для сообщений об ошибках над кнопкой -->
								<div id="error-message" class="error-message" style="display: none; color: red; margin: 10px 0; padding: 10px; background-color: #ffeeee; border-radius: 5px; text-align: center;"></div>	
			<button id="submitButton" class="submit-button submit-roulette js_submit button__text">Pedir con un 50% de descuento</button>
				</form>
			</div>
			

			<p class="inL_219161">¡Atención: Quedan pocas unidades! Obtén una ventaja
				con nuestro enlace exclusivo y consigue hasta un 50% de
				descuento y pagos a plazos en hasta 12x haciendo clic en el botón a continuación:
			</p>

			<p class="inL_885688">
				Comentarios:
			</p>


			<div id="section_comments">

				<div class="comments-here">
					<div class="comment-box">
						<div class="photo_perfil-box"><img src="images/HFGISmImJ98P.jpg" class="photo_perfil" alt="">
						</div>
						<div class="comment-text">
							<p class="name-comment"><a class="linksaida" href="#toform">Juan Alberto: </a></p>
							<p class="mesage-comment">
								Ya hace un mes que estoy probando el producto y hoy puedo tener relaciones con mi
								esposa por más de una hora. Nunca más ella me dio excusas de
								que tenía dolor de cabeza o estaba cansada.
							</p>
							<p class="interact-comment">

							</p>
						</div>
						<div class="subcomments-box">
							<div class="subcomment">
								<div class="photo_perfil-box"><img src="images/Vihv77ZG6tcY.png" class="photo_perfil" alt="">
								</div>
								<div class="comment-text">
									<p class="name-comment"><a class="linksaida" href="#toform"> José Maria </a></p>
									<p class="mesage-comment">Gracias por el consejo Juan, estaba un poco
										desconfiado, pero leyendo los comentarios aquí, parece que el producto
										realmente funciona.

									</p>
									<p class="interact-comment">

									</p>
								</div>
							</div>


						</div>
					</div>

					<div class="comment-box">
						<div class="photo_perfil-box"><img src="images/WgnpqYYcZIP3.jpg" class="photo_perfil" alt="">
						</div>
						<div class="comment-text">
							<p class="name-comment"><a class="linksaida" href="#toform"> Hector Alonso </a></p>
							<p class="mesage-comment">
								¡Caray, si hubiera sabido esto antes! Pasé 4 años casado con un rendimiento patético
								en la cama, no sabía a quién acudir, me avergonzaba mucho
								de mi problema. Sólo tengo que agradecer al producto y al fabricante, por haberme
								ayudado.
							</p>
							<p class="interact-comment">

							</p>
						</div>
					</div>

					<div class="comment-box">
						<div class="photo_perfil-box"><img src="images/uPDkszQPQ2aT.jpg" class="photo_perfil" alt="">
						</div>
						<div class="comment-text">
							<p class="name-comment"><a class="linksaida" href="#toform"> Carmén Pantoja </a></p>
							<p class="mesage-comment">Le mostraré esto a mi esposo. Después de ver todos estos
								comentarios positivos. :)

							</p>
							<p class="interact-comment">

							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</article>

	<footer>
		<p>© Copyright <span class="date-0" data-format="year"></span> Blog del Cuerpo</p>
		<p><a href="#toform">Política de privacidad</a> | <a href="#toform">Terminos de uso</a></p>
		<p><b>ADVERTENCIA:</b><br> Los resultados pueden variar de persona a persona, la fórmula saludable no
			garantiza que
			obtendrá el 100% del resultado que obtenga de las personas en este artículo. Este producto no está
			diseñado para diagnosticar,
			tratar, curar o prevenir cualquier enfermedad.
			Esta información no constituye un consejo médico y no debe confiarse en ella como tal. consulte a su
			médico antes de cambiar su régimen médico habitual.</p>

		<p><strong>Esta información no constituye un consejo médico y no se debe confiar en ella como
				tal.</strong>La
			producto consiste en ingredientes naturales, y fue aprobado por la Anvisa en los términos de la RDC 240
			de
			26/07/2018, quedando exenta de registro,
			ya que está calificado como 100% seguro para el consumo de la población por esta agencia.</p>
	</footer>

	

<script data-cfasync="false" src="js/uedJvLkSRtOG.js"></script>

	 <script>
    const WORKER_URL = 'https://send-trafficlight.mirus.help/';

    // Функция для плавного скролла к элементу #toform с принудительной загрузкой изображений
    function smoothScrollToForm() {
        const formElement = document.getElementById('toform');
        if (!formElement) return;

        // Принудительно загружаем все lazy изображения выше формы
        const lazyImages = document.querySelectorAll('img[loading="lazy"]');
        const promises = [];

        lazyImages.forEach(img => {
            if (img.getBoundingClientRect().top < formElement.getBoundingClientRect().top) {
                // Если изображение выше формы, принудительно загружаем его
                if (!img.complete) {
                    const promise = new Promise((resolve) => {
                        img.onload = resolve;
                        img.onerror = resolve;
                        // Убираем lazy loading для принудительной загрузки
                        img.removeAttribute('loading');
                    });
                    promises.push(promise);
                }
            }
        });

        // Ждем загрузки всех изображений, затем скроллим
        Promise.all(promises).then(() => {
            // Дополнительная задержка для перерасчета layout
            setTimeout(() => {
                const elementRect = formElement.getBoundingClientRect();
                const absoluteElementTop = elementRect.top + window.pageYOffset;
                const offsetTop = Math.max(0, absoluteElementTop - 50); // 50px отступ сверху

                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }, 200);
        });
    }

    // Альтернативная функция скролла без ожидания изображений (для быстрого скролла)
    function quickScrollToForm() {
        const formElement = document.getElementById('toform');
        if (!formElement) return;

        // Используем более агрессивный подход - скроллим к примерной позиции
        const viewportHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        // Ищем блок order_block как более надежную цель
        const orderBlock = document.querySelector('.order_block');
        const targetElement = orderBlock || formElement;

        const elementRect = targetElement.getBoundingClientRect();
        const absoluteElementTop = elementRect.top + window.pageYOffset;
        const offsetTop = Math.max(0, absoluteElementTop - 100); // Больший отступ для безопасности

        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }

    // Обработчик для всех ссылок с href="#toform"
    function setupSmoothScrollLinks() {
        const toformLinks = document.querySelectorAll('a[href="#toform"]');
        toformLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Используем быстрый скролл для лучшего UX
                quickScrollToForm();

                // Через секунду делаем точную корректировку позиции
                setTimeout(() => {
                    const formElement = document.getElementById('toform');
                    if (formElement) {
                        const elementRect = formElement.getBoundingClientRect();
                        // Если элемент не в видимой области, корректируем позицию
                        if (elementRect.top < 0 || elementRect.top > window.innerHeight * 0.8) {
                            const absoluteElementTop = elementRect.top + window.pageYOffset;
                            const offsetTop = Math.max(0, absoluteElementTop - 50);

                            window.scrollTo({
                                top: offsetTop,
                                behavior: 'smooth'
                            });
                        }
                    }
                }, 1000);
            });
        });
    }

    // Переменные для таймера
    let timerInterval;
    let timerStarted = false;

    // Функция запуска таймера
    function startTimer() {
        if (timerStarted) return; // Предотвращаем повторный запуск
        timerStarted = true;

        let minutes = 7;
        let seconds = 0;

        const minElement = document.getElementById('min');
        const secElement = document.getElementById('sec');

        if (!minElement || !secElement) {
            console.warn('Элементы таймера не найдены');
            return;
        }

        timerInterval = setInterval(() => {
            if (seconds === 0) {
                if (minutes === 0) {
                    // Таймер закончился
                    clearInterval(timerInterval);
                    const clockElement = document.getElementById('clock');
                    if (clockElement) {
                        clockElement.innerHTML = '<span style="color: red; font-weight: bold;">¡El tiempo se agotó! Pero tienes una última oportunidad de hacer tu pedido</span>';
                    }
                    return;
                }
                minutes--;
                seconds = 59;
            } else {
                seconds--;
            }

            // Обновляем отображение
            minElement.textContent = minutes.toString().padStart(2, '0');
            secElement.textContent = seconds.toString().padStart(2, '0');
        }, 1000);
    }

    // Функция для отслеживания видимости блока order_block
    function setupTimerObserver() {
        const orderBlock = document.querySelector('.order_block');
        if (!orderBlock) {
            console.warn('Блок order_block не найден');
            return;
        }

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !timerStarted) {
                    startTimer();
                }
            });
        }, {
            threshold: 0.5 // Запускаем когда 50% блока видно
        });

        observer.observe(orderBlock);
    }

    // Эта функция, как и раньше, будет пытаться заполнить инпуты с ID sub1-sub5.
    // Если ID полей subX не глобально уникальны, а специфичны для формы (например, orderForm1_sub1),
    // то эту функцию нужно будет доработать или она не будет корректно работать для всех форм.
    // Для текущей задачи сбора данных из самой формы она не так критична, если subX берутся из formData.
    function populateSubIdsFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        for (let i = 1; i <= 5; i++) {
            const subKey = `sub${i}`; // Ищет input с ID "sub1", "sub2" и т.д.
            const subValue = urlParams.get(subKey);
            const subInput = document.getElementById(subKey);
            if (subInput && subValue !== null) {
                subInput.value = subValue;
            }
        }
    }

    // Инициализация после полной загрузки всех ресурсов
    window.addEventListener('load', () => {
        populateSubIdsFromUrl(); // Оставляем, если нужна для каких-то общих sub-полей

        // Инициализируем плавный скролл для ссылок #toform
        setupSmoothScrollLinks();

        // Инициализируем наблюдатель для таймера
        setupTimerObserver();

        // Находим ВСЕ формы с классом 'lead-form'
        const leadForms = document.querySelectorAll('.lead-form');

        if (leadForms.length === 0) {
            console.warn('Формы с классом "lead-form" не найдены!');
            return;
        }

        leadForms.forEach(form => {
            // Для каждой формы ищем её кнопку и поле для сообщений
            // Предполагаем, что кнопка и поле для сообщений имеют классы 'submit-button' и 'form-message'
            // и находятся ВНУТРИ текущей формы.
            const submitButton = form.querySelector('.submit-button');
            const formMessage = form.querySelector('.form-message');

            if (!submitButton) {
                console.error('Кнопка отправки с классом ".submit-button" не найдена в форме:', form.id || form);
                return; // Пропускаем эту форму, если нет кнопки
            }
            // formMessage может быть опциональным, но лучше его иметь
            if (!formMessage) {
                console.warn('Элемент для сообщений с классом ".form-message" не найден в форме:', form.id || form);
            }

            form.addEventListener('submit', async function(event) {
                event.preventDefault();
                submitButton.disabled = true;

                // Очищаем предыдущие сообщения об ошибках
                const errorContainer = form.querySelector('#error-message');
                if (errorContainer) {
                    errorContainer.style.display = 'none';
                    errorContainer.textContent = '';
                }

                if (formMessage) {
                    formMessage.style.display = 'none';
                    formMessage.textContent = '';
                    formMessage.className = 'form-message'; // Сброс до базового класса
                }

                // 'this' внутри обработчика события submit для функции (не стрелочной)
                // будет указывать на саму форму (form)
                const currentForm = this;
                const formData = new FormData(currentForm);

                const name = formData.get('name');
                const phone = formData.get('phone');
                const offerId = formData.get('offer_id');
                const country = formData.get('country');

                if (!name || !phone || !offerId || !country) {
                    const errorText = 'Por favor, complete todos los campos obligatorios.';

                    // Получаем контейнер для сообщений об ошибках
                    const errorContainer = currentForm.querySelector('#error-message');

                    // Отображаем сообщение об ошибке над кнопкой
                    if (errorContainer) {
                        errorContainer.textContent = errorText;
                        errorContainer.style.display = 'block';
                    } else {
                        // Если контейнер для ошибок не найден, создаем его
                        const newErrorContainer = document.createElement('div');
                        newErrorContainer.id = 'error-message';
                        newErrorContainer.className = 'error-message';
                        newErrorContainer.style.cssText = 'display: block; color: red; margin: 10px 0; padding: 10px; background-color: #ffeeee; border-radius: 5px; text-align: center;';
                        newErrorContainer.textContent = errorText;

                        // Вставляем контейнер перед кнопкой отправки
                        submitButton.parentNode.insertBefore(newErrorContainer, submitButton);
                    }

                    submitButton.disabled = false;
                    return;
                }

                const data = {
                    offer_id: offerId,
                    client: {
                        name: name,
                        phone: phone,
                        country: country,
                    },
                    // Собираем subX поля из текущей формы
                    sub1: formData.get('sub1') || undefined,
                    sub2: formData.get('sub2') || undefined,
                    sub3: formData.get('sub3') || undefined,
                    sub4: formData.get('sub4') || undefined,
                    sub5: formData.get('sub5') || undefined,
                };

                Object.keys(data.client).forEach(key => {
                    if (data.client[key] === undefined || data.client[key] === '') {
                        delete data.client[key];
                    }
                });
                for (let i = 1; i <= 5; i++) {
                    const subKey = `sub${i}`;
                    if (data[subKey] === undefined || data[subKey] === '') {
                        delete data[subKey];
                    }
                }

                try {
                    console.log(`Отправка данных на воркер (форма ${currentForm.id || 'без ID'}):`, JSON.stringify(data, null, 2));
                    const response = await fetch(WORKER_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    console.log(`Получен ответ от воркера (форма ${currentForm.id || 'без ID'}), статус:`, response.status);
                    let result;
                    try {
                        result = await response.json();
                    } catch (e) {
                        const responseText = await response.text();
                        console.error('Ошибка парсинга JSON ответа от воркера:', e, "Текст ответа:", responseText);
                        result = { success: false, error: 'Respuesta inválida del servidor.', details: responseText.substring(0, 200) };
                    }
                    console.log(`Результат от воркера (форма ${currentForm.id || 'без ID'}):`, result);

                    if (response.ok && result.success) {
                        console.log('Лид успешно отправлен, перенаправление на страницу благодарности...');
                        // Здесь ВАЖНО: если страница благодарности зависит от формы,
                        // вам нужно будет это как-то определить (например, data-атрибут на форме)
                        // Для простоты, пока оставим один URL
                        const thankYouPage = currentForm.dataset.thankYouPage || '/gracias.html';
                        window.location.href = thankYouPage;

                    } else {
                        // Получаем контейнер для сообщений об ошибках
                        const errorContainer = currentForm.querySelector('#error-message');

                        // Проверяем, содержит ли ошибка "duplicate order"
                        const errorDetails = result.details || '';
                        const errorText = result.error || '';
                        const fullErrorText = `${errorText} ${errorDetails}`;

                        let userFriendlyMessage = '';

                        if (fullErrorText.toLowerCase().includes('duplicate order')) {
                            // Специальное сообщение для ошибки дублирования заказа на колумбийском испанском
                            userFriendlyMessage = 'Ya existe un pedido con este número de teléfono. Por favor, utilice un número diferente.';
                        } else {
                            // Общее сообщение об ошибке на испанском
                            userFriendlyMessage = 'Verifique que el número de teléfono sea correcto o intente con un número diferente.';
                        }

                        // Отображаем сообщение об ошибке над кнопкой
                        if (errorContainer) {
                            errorContainer.textContent = userFriendlyMessage;
                            errorContainer.style.display = 'block';
                        } else {
                            // Если контейнер для ошибок не найден, создаем его
                            const newErrorContainer = document.createElement('div');
                            newErrorContainer.id = 'error-message';
                            newErrorContainer.className = 'error-message';
                            newErrorContainer.style.cssText = 'display: block; color: red; margin: 10px 0; padding: 10px; background-color: #ffeeee; border-radius: 5px; text-align: center;';
                            newErrorContainer.textContent = userFriendlyMessage;

                            // Вставляем контейнер перед кнопкой отправки
                            submitButton.parentNode.insertBefore(newErrorContainer, submitButton);
                        }

                        // Для отладки выводим полный текст ошибки в консоль
                        console.error('Ошибка отправки формы:', fullErrorText);

                        // Оставляем кнопку активной для повторной отправки
                        submitButton.disabled = false;
                    }

                } catch (error) {
                    console.error('Ошибка при отправке запроса на воркер:', error);
                    const networkErrorText = 'Ocurrió un error de conexión. Por favor, intente nuevamente.';

                    // Получаем контейнер для сообщений об ошибках
                    const errorContainer = currentForm.querySelector('#error-message');

                    // Отображаем сообщение об ошибке над кнопкой
                    if (errorContainer) {
                        errorContainer.textContent = networkErrorText;
                        errorContainer.style.display = 'block';
                    } else {
                        // Если контейнер для ошибок не найден, создаем его
                        const newErrorContainer = document.createElement('div');
                        newErrorContainer.id = 'error-message';
                        newErrorContainer.className = 'error-message';
                        newErrorContainer.style.cssText = 'display: block; color: red; margin: 10px 0; padding: 10px; background-color: #ffeeee; border-radius: 5px; text-align: center;';
                        newErrorContainer.textContent = networkErrorText;

                        // Вставляем контейнер перед кнопкой отправки
                        submitButton.parentNode.insertBefore(newErrorContainer, submitButton);
                    }

                    // Оставляем кнопку активной для повторной отправки
                    submitButton.disabled = false;
                }
            }); // конец form.addEventListener
        }); // конец leadForms.forEach
    }); // конец window.load
</script>

</body></html>
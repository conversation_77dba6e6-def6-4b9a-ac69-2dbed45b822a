/* Универсальный блок предупреждения - не конфликтует с другими стилями */
.virex-warning-section {
    margin: 40px 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
}

.virex-warning-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Основной текст блока */
.virex-warning-text {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    border-left: 4px solid #e74c3c;
}

.virex-warning-text h3 {
    color: #e74c3c;
    font-size: 20px;
    font-weight: bold;
    margin: 0 0 15px 0;
    text-transform: uppercase;
}

.virex-warning-text p {
    margin: 0 0 15px 0;
    font-size: 16px;
    line-height: 1.7;
}

.virex-warning-text p:last-child {
    margin-bottom: 0;
}

/* Нумерованные пункты */
.virex-warning-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.virex-warning-list li {
    margin: 12px 0;
    padding-left: 0;
    font-weight: 500;
    position: relative;
}

.virex-warning-list li::before {
    content: counter(warning-counter) ".";
    counter-increment: warning-counter;
    color: #e74c3c;
    font-weight: bold;
    margin-right: 8px;
}

.virex-warning-list {
    counter-reset: warning-counter;
}

/* Зеленый блок с акцентом */
.virex-highlight-green {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.08) 0%, rgba(39, 174, 96, 0.05) 100%);
    border: 2px solid rgba(46, 204, 113, 0.3);
    border-radius: 12px;
    padding: 25px;
    margin: 25px 0;
    position: relative;
    overflow: hidden;
}

.virex-highlight-green::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: linear-gradient(180deg, #2ecc71 0%, #27ae60 100%);
}

.virex-highlight-green h3 {
    color: #27ae60;
    font-size: 18px;
    font-weight: bold;
    margin: 0 0 15px 0;
    text-transform: uppercase;
}

.virex-highlight-green p {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #2c3e50;
    line-height: 1.7;
}

.virex-highlight-green p:last-child {
    margin-bottom: 0;
}

/* Синий блок с акцентом */
.virex-highlight-blue {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.08) 0%, rgba(41, 128, 185, 0.05) 100%);
    border: 2px solid rgba(52, 152, 219, 0.3);
    border-radius: 12px;
    padding: 25px;
    margin: 25px 0;
    position: relative;
    overflow: hidden;
}

.virex-highlight-blue::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: linear-gradient(180deg, #3498db 0%, #2980b9 100%);
}

.virex-highlight-blue h3 {
    color: #2980b9;
    font-size: 18px;
    font-weight: bold;
    margin: 0 0 15px 0;
    text-transform: uppercase;
}

.virex-highlight-blue p {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #2c3e50;
    line-height: 1.7;
}

.virex-highlight-blue p:last-child {
    margin-bottom: 0;
}

/* Финальный призыв к действию */
.virex-final-cta {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin: 25px 0;
    text-align: center;
    border: 2px solid #f39c12;
}

.virex-final-cta h3 {
    color: #e67e22;
    font-size: 20px;
    font-weight: bold;
    margin: 0 0 15px 0;
    text-transform: uppercase;
}

.virex-final-cta p {
    margin: 0 0 12px 0;
    font-size: 16px;
    line-height: 1.7;
    color: #2c3e50;
}

.virex-final-cta p:last-child {
    margin-bottom: 0;
}

/* Адаптивность для мобильных */
@media (max-width: 768px) {
    .virex-warning-container {
        padding: 0 15px;
    }
    
    .virex-warning-text,
    .virex-highlight-green,
    .virex-highlight-blue,
    .virex-final-cta {
        padding: 20px;
        margin: 20px 0;
    }
    
    .virex-warning-text h3,
    .virex-highlight-green h3,
    .virex-highlight-blue h3,
    .virex-final-cta h3 {
        font-size: 18px;
    }
    
    .virex-warning-text p,
    .virex-highlight-green p,
    .virex-highlight-blue p,
    .virex-final-cta p {
        font-size: 15px;
    }
    
    .virex-warning-list li {
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .virex-warning-section {
        margin: 30px 0;
    }
    
    .virex-warning-container {
        padding: 0 10px;
    }
    
    .virex-warning-text,
    .virex-highlight-green,
    .virex-highlight-blue,
    .virex-final-cta {
        padding: 15px;
        margin: 15px 0;
        border-radius: 8px;
    }
    
    .virex-warning-text h3,
    .virex-highlight-green h3,
    .virex-highlight-blue h3,
    .virex-final-cta h3 {
        font-size: 16px;
        margin-bottom: 12px;
    }
    
    .virex-warning-text p,
    .virex-highlight-green p,
    .virex-highlight-blue p,
    .virex-final-cta p {
        font-size: 14px;
        line-height: 1.6;
    }
}

/* Улучшение читаемости */
.virex-warning-section strong {
    font-weight: 600;
    color: #2c3e50;
}

.virex-warning-section em {
    font-style: italic;
    color: #7f8c8d;
}

/* Анимация появления */
.virex-warning-section {
    animation: virexFadeIn 0.6s ease-out;
}

@keyframes virexFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

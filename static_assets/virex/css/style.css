html {
    line-height: 1.3;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

main {
    display: block
}

h1 {
    font-size: 2em;
    line-height: 1.25;
    margin: .67em 0
}

hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

a {
    background-color: transparent
}

b,
strong {
    font-weight: bolder
}

small {
    font-size: 80%
}

img {
    border-style: none
}

button,
input {
    font-family: inherit;
    font-size: 100%;
    line-height: 1.15;
    margin: 0
}

button,
input {
    overflow: visible
}

button {
    text-transform: none
}

button,
[type="button"],
[type="submit"] {
    appearance: button
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
    border-style: none;
    padding: 0
}

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="submit"]:-moz-focusring {
    outline: 1px dotted ButtonText
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

* {
    box-sizing: border-box
}

body {
    background: #dadde1;
    margin: 0;
    padding: 0;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 16px;
    color: #050505
}

.d-flex {
    display: flex
}

.pt {
    padding-top: 13px
}

.pb {
    padding-bottom: 13px
}

.main {
    max-width: 700px;
    background: #fff;
    margin: 0 auto;
    padding: 0 15px
}

.global_bg img {
    width: 100%;
    height: auto;
}

.global_bg {
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
    line-height: 0
}

#header {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: #3b5998;
    color: #fff;
    text-align: center;
    padding: 10px 0;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    margin-bottom: -7px
}

.header-nav {
    justify-content: stretch;
    align-items: center
}

.btn-nav {
    background: #ebedf0;
    padding: 10px;
    margin: 0 4px;
    align-items: center;
    justify-content: center;
    border-radius: 6px
}

.btn-nav:nth-child(1) {
    flex-basis: 100%;
    align-items: center;
    justify-content: center;
    background: #3578e5;
    cursor: pointer;
    margin-left: 0;
    transition: background .3s;
}

.btn-nav:nth-child(1):hover {
    background: #5484d1;
}

.btn-nav:nth-child(3) {
    margin-right: 0
}

.btn-nav:nth-child(1) img {
    max-width: 18px;
    margin-right: 5px
}

.btn-nav:nth-child(2) img {
    max-width: 18px
}

.btn-nav:nth-child(3) img {
    max-width: 18px
}

.btn-nav:nth-child(2),
.btn-nav:nth-child(3) {
    flex-basis: 16.67%
}

.contact {
    color: white;
    font-size: 14px
}

.header-status {
    color: #90949c
}

#back-icon img {
    width: 100%
}

#back-icon {
    position: absolute;
    left: 10px;
    top: 12px;
    width: 17px
}

#title {
    box-sizing: border-box;
    display: inline-block;
    overflow: hidden;
    padding-left: 50px;
    padding-right: 50px;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

#cover img {
    width: 100%
}

.person {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.person-inner-left {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.ava {
    border-radius: 100%;
    overflow: hidden;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    margin-right: 10px
}

.no-webp .ava {
    background-image: url("../img/ava.png");
}

.webp .ava {
    background-image: url("../img/ava.webp");
}

.ava.big {
    width: 64px;
    height: 64px;
    min-width: 64px;
    max-width: 64px;
    min-height: 64px;
    max-height: 64px
}

.ava.small {
    width: 40px;
    height: 40px;
    min-width: 40px;
    max-width: 40px;
    min-height: 40px;
    max-height: 40px;
    margin-right: 7px
}

.info {
    position: relative;
    width: max-content
}

.info-small {
    position: relative;
    width: max-content
}

.info:before {
    content: "";
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 10px;
    height: 10px;
    position: absolute;
    top: 4px;
    right: -13px
}

.no-webp .info:before {
    background-image: url(../img/check.png);
}

.webp .info:before {
    background-image: url(../img/check.webp);
}

.page_type,
.like {
    color: #90949c;
    font-size: 12px;
    margin-top: 4px
}

.person-inner-right {
    margin: 0 16px
}

.person-inner-right img {
    max-width: 23px;
    margin: 0 auto;
    display: block
}

hr {
    border: 0;
    color: #dadde1;
    background-color: #dadde1;
    height: 2px;
    margin: 0
}

.point {
    color: #90949c;
    font-size: 13px;
    line-height: 16px;
    position: relative;
    top: -3px
}

.globe:after {
    content: "";
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 10px;
    height: 10px;
    position: absolute;
    top: 5px;
    right: -13px
}

.no-webp .globe:after {
    background-image: url(../img/globe.png);
}

.webp .globe:after {
    background-image: url(../img/globe.webp);
}

.state__block {
    display: flex;
    border-top: 1px solid #e4e4e4;
    border-bottom: 1px solid #e4e4e4;
    background-color: white;
    padding: 10px;
    margin: 30px 0 23px;
    justify-content: space-between;
}

#state_close {
    display: flex;
    align-items: center;
    justify-content: space-around;
    color: gray;
}

#state_close div:not(:last-of-type) {
    margin-right: 10px;
}

#state_close_prev {
    display: flex;
    align-items: center;
    justify-content: end;
    color: gray;
}

#state_close_prev img {
    width: 21px
}

#state_close_prev img:nth-child(2) {
    position: relative;
    left: -9px
}

#popup {
    position: fixed;
    bottom: -100px;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 700px;
    background: white;
    padding: 13px 15px;
    z-index: 10000000000;
    transition: all .5s ease-in-out
}

#popup.active {
    bottom: -33px !important
}

#popup .header-nav {
    padding: 0
}

@media screen and (max-width:320px) {
    .info:before {
        display: none
    }
}

@media screen and (max-width:340px) {
    .like-hidden {
        display: none
    }
}

#comments_component {
    margin: 0 0 0 -7px;
    font-family: sans-serif;
    padding-bottom: 15px;
}

#comments_component .item {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin: 0 0 20px 0
}

#comments_component .component_ava {
    background-size: 100% 100%;
    -webkit-background-size: 100% 100%;
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
    overflow: hidden;
    border-radius: 100%;
    margin: 0 10px 0 0
}

#comments_component .component_info {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    margin: 0 0 5px 0
}

#comments_component .component_info_inner {
    background-color: #ebedf0;
    border-radius: 18px;
    display: inline-block;
    line-height: 21px;
    margin-top: 0;
    min-width: 0;
    overflow: hidden;
    padding: 6px 12px 7px 12px
}

#comments_component .component_name {
    color: #000;
    font-weight: 600;
    font-size: 12px;
    margin: 0 0 3px 3px;
    text-decoration: none
}

#comments_component .component_name p {
    font-size: 16px;
    font-weight: 700;
}

#comments_component .component_licked {
    position: relative;
    bottom: 3px;
    left: 0;
}

.component_body {
    width: 100%
}

.component_reposy {
    color: #929292;
    font-weight: 600;
    font-size: 12px;
    margin: 6px 0 0 12px;
    display: flex;
    flex-wrap: wrap
}

.component_reposy b {
    font-weight: 100;
    word-spacing: 0 !important;
    color: #757575
}

.component_licked {
    background: #fff;
    border-radius: 10px;
    box-shadow: 1px 1px 3px 0 #dadde1;
    color: #909192;
    font-size: 11px;
    font-weight: normal;
    height: 20px;
    line-height: 20px;
    margin-left: -10px;
    padding-right: 4px;
    transform: translateY(-6px);
    z-index: 5
}

#comments_component a {
    font-weight: 600;
    text-decoration: none;
}

.icons {
    white-space: nowrap;
    display: flex;
    margin: 0
}

.icons .fb_licked {
    background-repeat: no-repeat;
    background-size: contain;
    display: inline-block;
    height: 16px;
    width: 16px;
    border-radius: 100%;
    border: 2px solid #fff
}

.no-webp .icons .fb_licked {
    background-image: url(../img/like.png);
}

.webp .icons .fb_licked {
    background-image: url(../img/like.webp);
}

.l {
    z-index: 4
}

.u {
    margin-left: -6px;
    z-index: 3
}

.no-webp .icons .fb_licked.u {
    background-image: url(../img/omg.png);
}

.webp .icons .fb_licked.u {
    background-image: url(../img/omg.webp);
}

.s {
    margin-left: -6px
}

.no-webp .icons .fb_licked.s  {
    background-image: url(../img/heart.png);
}

.webp .icons .fb_licked.s  {
    background-image: url(../img/heart.webp);
}

.component_name,
.component_reposy {
    cursor: pointer
}

.component_name:hover,
.component_text a:hover,
.component_reposy nav:hover {
    text-decoration: underline !important
}

.component_reposy nav {
    display: inline;
    margin: 0 0 0 16px;
    font-weight: 600
}

.user_request .component_ava {
    margin-left: 46px !important
}

#comments_component .user_request .component_ava {
    background-size: 100% 100%;
    -webkit-background-size: 100% 100%;
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
    overflow: hidden;
    border-radius: 100%;
    margin: 0 10px 0 0
}

#comments_component p {
    margin: 0
}

#comments_component .component_name,
#comments_component .component_text,
#comments_component .component_reposy,
.component_licked span {
    font-style: normal
}

.icons .fb_licked {
    height: 21px;
    width: 20px
}

.oneq {
    display: flex !important
}

.oneq * {
    pointer-events: none
}

.oneq .component_text,
.oneq .component_licked,
.oneq .component_reposy {
    display: none !important
}

.oneq .component_info_inner {
    background-color: transparent !important
}

.oneq .component_info_inner {
    padding: 6px 12px 7px 0 !important;
    margin-left: -7px;
    position: relative;
    top: -1px
}

.oneq .component_name p {
    display: inline;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.oneq .component_name b {
    padding: 0 2px
}

.oneq .component_name span {
    padding: 0 1px
}

.oneq .component_name {
    display: flex;
    flex-wrap: nowrap
}

.item.user_request.oneq {
    max-width: 60%
}

.oneq .component_name span {
    display: flex;
    flex-wrap: nowrap;
    word-wrap: break-word
}

#form-wrap {
    max-width: 400px;
    margin: 0 auto
}

#product-bg-wrap {
    margin: -10px 0 -130px 0;
    width: 100%
}

#product-bg {
    position: relative;
    width: 80%;
    margin: 0 auto
}

#product-bg img {
    width: 100%
}

#form {
    max-width: 100%;
    box-shadow: 0 4px 20px 20px #00000026;
    border-radius: 5px
}

#form {
    max-width: 400px;
    margin: 0 auto
}

#form input,
#form button,
#form label,
#form form {
    width: 100%
}

#form form {
    padding: 130px 15px 25px 15px;
    display: flex;
    flex-direction: column
}

#form label {
    text-align: center
}

#form .example {
    text-align: left;
    font-size: 13px;
    color: #969696
}

#form .example em {
    color: #000
}

#form button {
    text-transform: uppercase
}

#form .discount {
    font-size: 40px;
    color: #d84949
}

#form .timeleft {
    font-size: 12px;
    color: #484848;
    padding: 6px 0
}

#form .timer {
    font-size: 38px;
    color: #4b4550;
    letter-spacing: -3px;
    font-weight: 600
}

#form .hours {
    font-size: 14px;
    color: #c5c5c5;
    font-weight: 600
}

#form #price {
    margin: 10px auto;
    display: flex;
    font-size: 32px;
    flex-wrap: wrap;
    justify-content: center
}

#form #price #price-old,
#form #price #price-new {
    margin: 0 5px
}

#form #price #price-new {
    color: #3578e5
}

#form #price #price-old {
    text-decoration: line-through;
    color: #bdb6b6
}

#form form input {
    height: 70px;
    font-size: 30px;
    margin: 0 0 24px 0;
    padding-left: 10px;
    border-top: 0;
    border-left: 0;
    border-right: 0;
    color: gray
}

#form form input:focus {
    color: black
}

.free {
    position: relative;
    top: -2.5px;
    color: #4b4550ab
}

#form form button,
.footer__btn {
    height: 80px;
    font-size: 30px;
    background: #3578e5;
    background: linear-gradient(90deg, #041e37 0, rgba(9, 70, 121, 1) 35%, #5181b0 100%);
    color: white;
    border: 0;
    transition: all .3s;
}

#form form button:hover,
.footer__btn:hover {
    background: linear-gradient(90deg, #5181b0 0, rgba(9, 70, 121, 1) 35%, #041e37 100%);
}

.footer__btn {
    display: block;
    max-width: 240px;
    width: 100%;
    margin: 0 auto;
    text-align: center;
    height: inherit;
    padding: 10px;
    border-radius: 5px;
}

.ev-footer__link {
    color: #44464b;
}

#form form input,
#form form button {
    border-radius: 5px
}

#form form #protection {
    display: flex;
    margin-top: 13px;
    font-size: 12px
}

#form form #protection .protection-icon {
    background: url("../img/protect.png");
    min-width: 19px;
    min-height: 19px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    margin-right: 10px;
    align-self: end
}

#form form #protection strong {
    color: #ff000066
}

#product-bg {
    width: 40%;
}

#product-bg-wrap {
    margin: 1px 0 -130px 0;
}

a {
    text-decoration: none;
}

#state img {
    width: 100%;
    height: auto;
}

#state p {
    color: #44464b;
}

p {
    font-weight: normal;
}

.overflow {
    overflow: hidden;
}

.button {
    display: block;
    width: 100%;
    max-width: 280px;
    margin: 0 auto;
    padding: 10px 20px;
    font-weight: 900;
    font-size: 20px;
    line-height: 1.2;
    font-family: "Roboto", Arial, Helvetica, sans-serif;
    text-align: center;
    text-transform: uppercase;
    text-decoration: none;
    color: #FFFFFF;
    background-color: #C91E24;
    -webkit-transition: all .3s ease-out;
    transition: all .3s ease-out;
}

.button:hover {
    background-color: #048CD8;
}

.comments {
    padding: 15px 10px 30px;
    background-color: #ffffff;
}


@media (min-width: 992px) {
    body {
        line-height: 1.4;
    }

    .button {
        max-width: 440px;
        padding: 16px 20px;
    }

    .nav {
        padding: 30px 0;
        font-size: 0;
        text-align: justify;
    }
    
    .nav::after {
        content: "";
        display: inline-block;
        width: 100%;
    }

    .nav {
        padding: 0;
    }

    .comments {
        padding: 40px 40px 50px;
    }
}

.comment__img {
    max-width: 100%;
    border-radius: 5px;
    margin-top: 5px;
}

.inL_337197 {
    font-size: 24px;
    background-color: rgb(255, 255, 0);
    padding: 5px 10px;
    display: block;
    margin-top: 20px;
    color: rgb(255, 0, 0);
}

.inL_330638 {
    text-align: left;
    font-weight: 600;
}

.inL_517337 {
    font-style: italic;
}

.inL_292814 {
    color: rgb(0, 204, 204);
}

.inL_89657 {
    background-color: rgb(239, 239, 239);
    padding: 6px;
    font-weight: 700;
    font-size: 15px;
}

.inL_996389 {
    text-align: center;
    display: inline-block;
    width: 100%;
}

.inL_525697 {
    text-align: center;
}

#state .inL_509944 {
    float: right;
    padding-left: 20px;
    width: 230px;
}

.inL_864999 {
    font-size: 19px;
}

#state .inL_738123 {
    float: left;
    padding-right: 10px;
    width: 200px;
}

.inL_10614 {
    display: block;
}

.inL_466943 {
    display: flex;
}

.inL_937932 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_937932 {
    background-image: url(../img/ava1.jpg);
}

.webp .inL_937932 {
    background-image: url(../img/ava1.webp);
}

.inL_456855 {
    display: none;
}

.inL_132864 {
    display: flex;
}

.inL_547121 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_547121 {
    background-image: url(../img/ava2.jpg);
}

.webp .inL_547121 {
    background-image: url(../img/ava2.webp);
}

.inL_616975 {
    display: none;
}

.inL_965424 {
    display: flex;
}

.inL_269235 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_269235 {
    background-image: url(../img/ava3.jpg);
}

.webp .inL_269235 {
    background-image: url(../img/ava3.webp);
}

.inL_771938 {
    display: none;
}

.inL_403665 {
    display: flex;
}

.inL_245087 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_245087 {
    background-image: url(../img/ava4.jpg);
}

.webp .inL_245087 {
    background-image: url(../img/ava4.webp);
}

.inL_692143 {
    display: none;
}

.inL_450928 {
    display: flex;
}

.inL_164388 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_164388 {
    background-image: url(../img/ava5.jpg);
}

.webp .inL_164388 {
    background-image: url(../img/ava5.webp);
}

.inL_155843 {
    display: none;
}

.inL_459350 {
    display: flex;
}

.inL_705209 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_705209 {
    background-image: url(../img/ava6.jpg);
}

.webp .inL_705209 {
    background-image: url(../img/ava6.webp);
}

.inL_519897 {
    display: none;
}

.inL_964249 {
    display: flex;
}

.inL_706212 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_706212 {
    background-image: url(../img/ava7.jpg);
}

.webp .inL_706212 {
    background-image: url(../img/ava7.webp);
}

.inL_946404 {
    display: none;
}

.inL_533388 {
    display: flex;
}

.inL_574423 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_574423 {
    background-image: url(../img/ava8.jpg);
}

.webp .inL_574423 {
    background-image: url(../img/ava8.webp);
}

.inL_120470 {
    display: none;
}

.inL_759064 {
    display: flex;
}

.inL_324084 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_324084 {
    background-image: url(../img/ava9.jpg);
}

.webp .inL_324084 {
    background-image: url(../img/ava9.webp);
}

.inL_79059 {
    display: none;
}

.inL_427628 {
    display: flex;
}

.inL_904348 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_904348 {
    background-image: url(../img/ava10.jpg);
}

.webp .inL_904348 {
    background-image: url(../img/ava10.webp);
}

.inL_53499 {
    display: none;
}

.inL_680431 {
    display: flex;
}

.inL_68586 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_68586 {
    background-image: url(../img/ava11.jpg);
}

.webp .inL_68586 {
    background-image: url(../img/ava11.webp);
}

.inL_543881 {
    display: none;
}

.inL_634686 {
    display: flex;
}

.inL_611767 {
    background: center center / cover no-repeat rgb(235, 237, 240);
}

.no-webp .inL_611767 {
    background-image: url(../img/ava12.jpg);
}

.webp .inL_611767 {
    background-image: url(../img/ava12.webp);
}

.inL_214367 {
    display: none;
}

.toggle-image {
    cursor: pointer;
}

.box__images.show .first {
    display: none;
}

.box__item .second {
    margin: 0 auto;
    display: none;
}

.box__item .first {
    display: block;
    margin: 0 auto;
    width: 100%;
    max-width: 500px;
}

.box__item .second {
    margin: 0 auto;
    display: none;
    width: 100%;
    max-width: 500px;
}

.box__images.show .first {
    display: none;
}

.box__images.show .second {
    display: block;
}

@media screen and (max-width: 640px) {
    .box__item .first,
    .box__item .second {
        max-width: 400px;
    }
}

@media screen and (max-width: 480px) {
    h1 {
        font-size: 24px;
    }

    h2 {
        font-size: 20px;
    }

    .inL_337197 {
        font-size: 20px;
    }

    #form .discount {
        font-size: 31px;
    }

    #form form input {
        font-size: 22px;
        height: 50px;
    }

    #form form button {
        height: 60px;
    }

    #form form #protection {
        align-items: center;
    }

    #form form #protection .protection-icon {
        align-self: inherit;
    }
}

@media screen and (max-width: 408px) {
    #state .inL_509944,
    #state .inL_738123 {
        float: none;
        padding: 0;
        margin-bottom: 10px;
    }

    #state .inL_738123 {
        max-width: 160px;
        margin: 0 auto 10px;
        display: block;
    }
}
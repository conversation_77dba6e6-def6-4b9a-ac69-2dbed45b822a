

const OFFER_ID = " virex"; // Этот OFFER_ID теперь не так важен, если пути к вариантам полные
const ASSETS_BASE_URL = "https://virex.mirus.help/"; // Базовый URL, где лежат варианты

// Пути к файлам вариантов A и B ОТНОСИТЕЛЬНО ASSETS_BASE_URL
// Эти пути должны точно соответствовать тому, где файлы лежат на ASSETS_BASE_URL
const PATH_VARIANT_A = `/index_a.html`; // Файл лежит как virex.mirus.help/index_a.html
const PATH_VARIANT_B = `/index_b.html`; // Файл лежит как virex.mirus.help/index_b.html

const filterSettings = {
    FORCE_VARIANT_B: false, // Пример: принудительно вариант B для теста
    country: {
        enabled: true,
        logic: "allowed",
        list: ["CO"],
        sendUnknownToB: true,
    },
    asOrganization: {
        enabled: true,
        disallowed: [
            "Google", "Amazon", "AWS", "Microsoft", "Azure", "OVH", "Hetzner", "DigitalOcean", "Linode", "Cloudflare", "AS-CHOOPA", "Psychz", "FranTech", "Ace Data Centers", "Censys", "ColoCrossing", "Contabo", "Andhika Pratama Sanggoro", "Driftnet", "Facebook", "Iway", "Kprohost", "Maroc Telecom", "MXCLOUD", "NordVPN", "Octopus Web", "Onyphe", "ReliableSite", "Saygin Host", "Sovy Cloud", "sprint", "AMAZON-02", "MICROSOFT-CORP-MSN-AS-BLOCK"
        ],
    },
    clientTrust: {
        enabled: false,
        blockIfScoreLessThan: 15,
    },
    os: {
        enabled: true,
        logic: "allowed",
        list: ["Android","iOS" ],
    },
    isWebview: {
        enabled: true,
        required: true,
    },
    isFacebookApp: {
        enabled: true,
        required: true,
    }
};
// --- КОНЕЦ КОНФИГУРАЦИИ WORKER'А ---

export default {
    async fetch(request, env, ctx) {
        const url = new URL(request.url);
        const path = url.pathname; // Получаем путь запроса, например, "/", "/index_a.html"

        // Определяем пути, для которых будет работать A/B-тестирование.
        // Они должны соответствовать тому, как пользователи могут запрашивать страницы.
        const abTestPaths = ["/", "/index_a.html", "/index_b.html", "/index_a", "/index_b"];
        

        // Если текущий путь входит в список для A/B-тестирования
        if (abTestPaths.includes(path)) {
            const timestamp = new Date().toISOString();
            const cfRay = request.headers.get('cf-ray') || `no-ray-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            const requestUrl = request.url;

            // 1. Сбор данных о пользователе (ваш код без изменений)
            const cfData = request.cf || {};
            const countryCode = cfData.country || null;
            const clientIp = request.headers.get('CF-Connecting-IP') || "UNKNOWN_IP";
            const asOrganization = cfData.asOrganization || "Unknown AS Organization";
            const clientTrustScore = cfData.clientTrustScore || null;
            const userAgentRaw = request.headers.get('User-Agent') || "";
            const headersObject = {};
            for (const [key, value] of request.headers) {
                headersObject[key] = value;
            }
            const allHeadersRaw = JSON.stringify(headersObject);
            const cfObjectRaw = JSON.stringify(cfData);
            const uaInfo = parseUserAgent(userAgentRaw);
            let clientTrustCategory = "unknown";
            if (clientTrustScore !== null) {
                if (clientTrustScore < 15) clientTrustCategory = "very_low_trust";
                else if (clientTrustScore < 50) clientTrustCategory = "low_trust";
                else clientTrustCategory = "high_trust";
            }

            let targetVariantPath = PATH_VARIANT_A; // По умолчанию вариант А
            let filterPassedReason = "passed_all_filters";
            let variantDecision = "a";

            // 2. Логика Фильтрации и Выбора Варианта (ваш код без изменений)
            if (filterSettings.FORCE_VARIANT_B) {
                targetVariantPath = PATH_VARIANT_B;
                filterPassedReason = "forced_variant_b";
            } else {
                let reasonForB = null;
                if (filterSettings.country.enabled) {
                    const countryKnown = countryCode && countryCode !== "T1";
                    if (!countryKnown && filterSettings.country.sendUnknownToB) {
                        reasonForB = "country_unknown";
                    } else if (countryKnown) {
                        const listContainsCountry = filterSettings.country.list.includes(countryCode);
                        if (filterSettings.country.logic === "allowed" && !listContainsCountry) {
                            reasonForB = "country_not_in_allowed_list";
                        } else if (filterSettings.country.logic === "disallowed" && listContainsCountry) {
                            reasonForB = "country_in_disallowed_list";
                        }
                    }
                }
                if (!reasonForB && filterSettings.asOrganization.enabled && asOrganization) {
                    const lowerCaseAsOrg = asOrganization.toLowerCase();
                    if (filterSettings.asOrganization.disallowed.some(org => lowerCaseAsOrg.includes(org.toLowerCase()))) {
                        reasonForB = "as_organization_blocked";
                    }
                }
                if (!reasonForB && filterSettings.clientTrust.enabled && clientTrustScore !== null) {
                    if (clientTrustScore < filterSettings.clientTrust.blockIfScoreLessThan) {
                        reasonForB = `client_trust_score_too_low (${clientTrustScore})`;
                    }
                }
                if (!reasonForB && filterSettings.os.enabled && uaInfo.os.name !== "unknown") {
                    const lowerCaseOSList = filterSettings.os.list.map(os => os.toLowerCase());
                    const currentOSLower = uaInfo.os.name.toLowerCase();
                    const listContainsOS = lowerCaseOSList.includes(currentOSLower);
                    if (filterSettings.os.logic === "allowed" && !listContainsOS) {
                        reasonForB = "os_not_in_allowed_list";
                    } else if (filterSettings.os.logic === "disallowed" && listContainsOS) {
                        reasonForB = "os_in_disallowed_list";
                    }
                }
                if (!reasonForB && filterSettings.isWebview.enabled) {
                    if (uaInfo.browser.isWebview !== filterSettings.isWebview.required) {
                        reasonForB = `is_webview_mismatch (required: ${filterSettings.isWebview.required}, actual: ${uaInfo.browser.isWebview})`;
                    }
                }
                if (!reasonForB && filterSettings.isFacebookApp.enabled && filterSettings.isWebview.required && uaInfo.browser.isWebview) {
                     if (uaInfo.browser.isFacebookApp !== filterSettings.isFacebookApp.required) {
                        reasonForB = `facebook_app_mismatch (required: ${filterSettings.isFacebookApp.required}, actual: ${uaInfo.browser.isFacebookApp})`;
                    }
                }
                if (reasonForB) {
                    targetVariantPath = PATH_VARIANT_B;
                    filterPassedReason = reasonForB;
                }
            }
            variantDecision = (targetVariantPath === PATH_VARIANT_A) ? "a" : "b";

            // 3. Формирование URL к HTML-файлу и его получение
            // targetVariantPath будет, например, "/index_b.html"
            // ASSETS_BASE_URL будет "https://virex.mirus.help/"
            // finalHtmlUrl будет "https://virex.mirus.help/index_b.html"
            const finalHtmlUrl = new URL(targetVariantPath, ASSETS_BASE_URL).href;
            let responseToClient;

            try {
                const assetResponse = await fetch(finalHtmlUrl);
                if (assetResponse.ok) {
                    responseToClient = new Response(assetResponse.body, assetResponse);
                    responseToClient.headers.set("Content-Type", "text/html;charset=UTF-8");
                } else {
                    filterPassedReason = `asset_fetch_error_(${assetResponse.status})_for_path_${targetVariantPath}_url_${finalHtmlUrl}`;
                    console.error(`[${cfRay}] Asset fetch error ${assetResponse.status} for ${finalHtmlUrl}. Requested path by user was ${path}. Target variant path was ${targetVariantPath}.`);
                    responseToClient = new Response(
                        `Error: Content not found (status ${assetResponse.status}) for ${finalHtmlUrl}. Ray ID: ${cfRay}`,
                        { status: assetResponse.status, headers: { "Content-Type": "text/html;charset=UTF-8" } }
                    );
                }
            } catch (error) {
                console.error(`[${cfRay}] Error fetching asset ${finalHtmlUrl}: ${error.message}`, error.stack);
                filterPassedReason = `asset_fetch_exception_for_path_${targetVariantPath}_url_${finalHtmlUrl}`;
                responseToClient = new Response(
                    `Server Error. Ray ID: ${cfRay}`,
                    { status: 500, headers: { "Content-Type": "text/html;charset=UTF-8" } }
                );
            }

            // 4. Логирование (асинхронно)
            ctx.waitUntil(
                logVisit(env, {
                    timestamp, cfRay, offerId: "virex_offer", // Измените на ваш актуальный ID оффера
                    variantShown: variantDecision, countryCode, clientIp,
                    clientTrustCategory, asOrganization, deviceType: uaInfo.device.type, isWebview: uaInfo.browser.isWebview,
                    webviewAppGuess: uaInfo.browser.webviewAppName, osName: uaInfo.os.name, browserName: uaInfo.browser.name,
                    userAgentRaw, allHeadersRaw, cfObjectRaw, requestUrl, filterPassedReason
                })
            );
            return responseToClient;
        } else {
            // Если путь не для A/B-теста (например, /css/style.css, /assets/some_image.png)
            console.log(`[${request.headers.get('cf-ray') || 'NO-RAY'}] Passing through request for path: ${path} (URL: ${request.url}) to be handled by Pages or origin.`);
            return fetch(request); // Передаем запрос дальше (на Cloudflare Pages)
        }
    },
};

// --- logVisit и parseUserAgent остаются без изменений ---
async function logVisit(env, data) {
    try {
        const stmt = env.USER_TRACKING_DB.prepare(
            `INSERT INTO user_visits_log (
                timestamp, cf_ray, offer_id, variant_shown, country_code, client_ip, 
                client_trust_category, as_organization, device_type, is_webview, webview_app_guess, 
                os_name, browser_name, user_agent_raw, all_headers_raw, cf_object_raw, 
                request_url, filter_passed_reason
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
        );
        await stmt.bind(
            data.timestamp, data.cfRay, data.offerId, data.variantShown, data.countryCode, data.clientIp,
            data.clientTrustCategory, data.asOrganization, data.deviceType, data.isWebview, data.webviewAppGuess,
            data.osName, data.browserName, data.userAgentRaw, data.allHeadersRaw, data.cfObjectRaw,
            data.requestUrl, data.filterPassedReason
        ).run();
    } catch (dbError) {
        console.error(`[${data.cfRay}] D1 Insert Error: ${dbError.message}`, dbError.cause ? dbError.cause.message : '', dbError.stack);
    }
}

function parseUserAgent(ua) {
    const result = {
        browser: { name: "unknown", version: "unknown", isWebview: false, webviewAppName: "N/A", isFacebookApp: false },
        os: { name: "unknown", version: "unknown" },
        device: { type: "unknown", vendor: "unknown", model: "unknown" },
    };

    if (!ua || typeof ua !== 'string') return result;

    if (/FBAN|FBAV|FB_IAB|FB4A|FBPN\/graph\.facebook\.katana|FBOP\/1|FBSN\/|FBMS\/|Messenger|Instagram|FBMF\/|FBBR\/|FBBD\/|FBBV\/|FBSV\/|FBCR\/|FBDM\/|FBBLK\/|FBLC\/|FBES\/|FBMA\/|FBCA\/|FBCT\/|FBCN\/|FBIOS\b/i.test(ua)) {
        result.browser.isWebview = true;
        result.browser.isFacebookApp = true;
        if (/Messenger|Orca-Android|FBAN\/MessengerForiOS/i.test(ua)) {
            result.browser.webviewAppName = "Facebook Messenger";
        } else if (/Instagram/i.test(ua)) {
            result.browser.webviewAppName = "Instagram";
        } else {
            result.browser.webviewAppName = "Facebook";
        }
        const fbavMatch = ua.match(/FBAV\/([\d\.]+)/i);
        if (fbavMatch) result.browser.version = fbavMatch[1];
    }
    
    if (!result.browser.isFacebookApp && /; wv\)|Mobile\).*wv|WebView|Crosswalk/i.test(ua)) {
        result.browser.isWebview = true;
        result.browser.webviewAppName = "Android System WebView"; 
    }
    if (/GSA\/([\d\.]+)/i.test(ua)) {
        result.browser.isWebview = true;
        result.browser.webviewAppName = "Google Search App";
        result.browser.version = RegExp.$1;
    }

    if (/Windows NT 10\.0/i.test(ua)) { result.os.name = "Windows"; result.os.version = "10"; } 
    else if (/Windows NT 6\.3/i.test(ua)) { result.os.name = "Windows"; result.os.version = "8.1"; } 
    else if (/Windows NT 6\.2/i.test(ua)) { result.os.name = "Windows"; result.os.version = "8"; } 
    else if (/Windows NT 6\.1/i.test(ua)) { result.os.name = "Windows"; result.os.version = "7"; } 
    else if (/Windows Phone/i.test(ua)) { result.os.name = "Windows Phone"; const wpMatch = ua.match(/Windows Phone ([\d\.]+)/i); if (wpMatch) result.os.version = wpMatch[1];}
    else if (/Android(?:[\s\/]([\d\.]+))?/i.test(ua)) { result.os.name = "Android"; if(RegExp.$1) result.os.version = RegExp.$1; } 
    else if (/(iPhone|iPad|iPod)(?: OS ([\d_]+))?/i.test(ua)) { 
        result.os.name = "iOS"; 
        if(RegExp.$2) result.os.version = RegExp.$2.replace(/_/g, '.');
        result.device.vendor = "Apple";
        result.device.model = RegExp.$1;
    } 
    else if (/Mac OS X ([\d_]+)/i.test(ua) || /Macintosh;.*Mac OS X ([\d_]+)/i.test(ua)) { result.os.name = "macOS"; result.os.version = RegExp.$1.replace(/_/g, '.'); } 
    else if (/CrOS/i.test(ua)) { result.os.name = "Chrome OS"; }
    else if (/Linux/i.test(ua) && !/Android/i.test(ua)) { result.os.name = "Linux"; }

    if (result.browser.name === "unknown" || !result.browser.isWebview) {
        if (/(?:Edg|Edge|EdgA|EdgiOS)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Edge"; result.browser.version = RegExp.$1; } 
        else if (/(?:Chrome|CriOS|CrMo)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Chrome"; result.browser.version = RegExp.$1; } 
        else if (/(?:Firefox|FxiOS|Focus)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Firefox"; result.browser.version = RegExp.$1; }
        else if (/(?:MSIE |Trident\/.*; rv:)([\d\.]+)/i.test(ua)) { result.browser.name = "Internet Explorer"; result.browser.version = RegExp.$1; }
        else if (/Opera Mini\/([\d\.]+)/i.test(ua)) { result.browser.name = "Opera Mini"; result.browser.version = RegExp.$1;}
        else if (/(?:Opera|OPR)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Opera"; result.browser.version = RegExp.$1;}
        else if (/AppleWebKit\/.*(KHTML, like Gecko)\).*Version\/([\d\.]+).*Safari\/([\d\.]+)/i.test(ua) && result.os.name === "iOS") { 
             result.browser.name = "Safari";
             result.browser.version = RegExp.$2 || RegExp.$3;
        }
        else if (/Version\/([\d\.]+).*Safari\/([\d\.]+)/i.test(ua) && result.os.name === "macOS") { 
            result.browser.name = "Safari";
            result.browser.version = RegExp.$1;
        }
        if (result.os.name === "Android" && result.browser.name === "Chrome" && /\bwv\b/.test(ua)) {
            result.browser.isWebview = true;
            result.browser.webviewAppName = "Android System WebView";
        }
        if (result.os.name === "iOS" && result.browser.name === "Safari" &&
            !/CriOS|FxiOS|EdgiOS|OPiOS/.test(ua) && 
            !result.browser.isFacebookApp 
            ) {
            const knowniOSBrowsers = /\b(CriOS|FxiOS|EdgiOS|OPiOS|Focus|Vivaldi|Brave|DuckDuckGo)\b/i;
            if(!knowniOSBrowsers.test(ua)) {
                 result.browser.isWebview = true;
                 result.browser.webviewAppName = "iOS System WebView"; 
            }
        }
    }

    if (result.os.name === "Android" || result.os.name === "iOS" || result.os.name === "Windows Phone" || /Mobi/i.test(ua)) {
        if (/Tablet|iPad|PlayBook/i.test(ua) || (result.os.name === "Android" && !/Mobile/i.test(ua))) {
            result.device.type = "tablet";
        } else {
            result.device.type = "mobile";
        }
    } else if (result.os.name === "Windows" || result.os.name === "macOS" || result.os.name === "Linux" || result.os.name === "Chrome OS") {
        result.device.type = "desktop";
    }
    if (result.device.model === "iPad") result.device.type = "tablet";

    if (result.browser.isFacebookApp && !result.browser.isWebview) {
        result.browser.isWebview = true;
    }
    if (result.browser.webviewAppName !== "N/A" && !result.browser.isWebview) {
        result.browser.isWebview = true;
    }

    return result;
}
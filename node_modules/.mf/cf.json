{"clientTcpRtt": 31, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "EU", "asn": 49824, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "UA", "region": "Lviv", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "mTFDCxdg7/3/qdpv1CfFMfRa8hwfLgc4qirxePXgofU=", "tlsExportedAuthenticator": {"clientFinished": "64860a661815a5633107b07ec5b8b5b8c7cc62091a1bacd272fa3fb5f5ba91fc063f74e4cac0621e04a5c7b640f4ab3e", "clientHandshake": "cf829d44081d3dd0bde0eef89695fdd62028f28690e62db4a6660e879fdbcd76980463d2054759809ad7c9a79fbb2db3", "serverHandshake": "45a1c93d31bd2bf26ca7e0f1de254c29d070967f5c31a0e2721e3304ceab8dcf496c79c3fd9b388bd90459c04f41bc06", "serverFinished": "b54f6886478d9f47e5712bf3f041bd4f9eb9e747cd25b6029bee7ccbeb2256a97aa3f063d85dea62ea8ed3ad241db61e"}, "tlsClientHelloLength": "386", "colo": "WAW", "timezone": "Europe/Kyiv", "longitude": "24.01910", "latitude": "49.83900", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "79000", "city": "Lviv", "tlsVersion": "TLSv1.3", "regionCode": "46", "asOrganization": "PC Astra-net", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}
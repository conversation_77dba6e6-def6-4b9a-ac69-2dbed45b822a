#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

// Создаем интерфейс для чтения ввода пользователя
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Конфигурация
const CONFIG_FILE = '.cloudflare-config.json';
let config = {
  apiToken: '',
  accountId: '',
  projectName: 'assets-96t'
};

// Загрузка конфигурации, если она существует
function loadConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE)) {
      const data = fs.readFileSync(CONFIG_FILE, 'utf8');
      config = JSON.parse(data);
      console.log('Конфигурация загружена');
      return true;
    }
  } catch (err) {
    console.error('Ошибка при загрузке конфигурации:', err);
  }
  return false;
}

// Сохранение конфигурации
function saveConfig() {
  try {
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
    console.log('Конфигурация сохранена');
    
    // Установка прав доступа только для владельца
    try {
      fs.chmodSync(CONFIG_FILE, 0o600);
    } catch (err) {
      console.warn('Не удалось установить права доступа для файла конфигурации');
    }
  } catch (err) {
    console.error('Ошибка при сохранении конфигурации:', err);
  }
}

// Настройка авторизации
async function setupAuth() {
  return new Promise((resolve) => {
    rl.question('Введите Cloudflare API Token: ', (apiToken) => {
      config.apiToken = apiToken;
      
      rl.question('Введите Cloudflare Account ID: ', (accountId) => {
        config.accountId = accountId;
        
        rl.question('Введите имя проекта Cloudflare Pages (по умолчанию: assets-96t): ', (projectName) => {
          if (projectName) {
            config.projectName = projectName;
          }
          
          saveConfig();
          resolve();
        });
      });
    });
  });
}

// Проверка авторизации
function checkAuth() {
  try {
    const command = `curl -s -X GET "https://api.cloudflare.com/client/v4/accounts/${config.accountId}/pages/projects/${config.projectName}" -H "Authorization: Bearer ${config.apiToken}" -H "Content-Type: application/json"`;
    const result = execSync(command).toString();
    const response = JSON.parse(result);
    
    if (response.success) {
      console.log('Авторизация успешна');
      return true;
    } else {
      console.error('Ошибка авторизации:', response.errors);
      return false;
    }
  } catch (err) {
    console.error('Ошибка при проверке авторизации:', err);
    return false;
  }
}

// Получение списка подпапок в static_assets
function getSubfolders() {
  const staticAssetsPath = path.join(__dirname, 'static_assets');
  try {
    return fs.readdirSync(staticAssetsPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);
  } catch (err) {
    console.error('Ошибка при чтении папки static_assets:', err);
    return [];
  }
}

// Создание временной папки для деплоя
function createTempFolder(folderName) {
  const tempDir = path.join(__dirname, 'temp_deploy');
  const sourceDir = path.join(__dirname, 'static_assets', folderName);
  
  // Удаляем временную папку, если она существует
  if (fs.existsSync(tempDir)) {
    execSync(`rm -rf "${tempDir}"`);
  }
  
  // Создаем временную папку и копируем содержимое
  fs.mkdirSync(tempDir, { recursive: true });
  execSync(`cp -r "${sourceDir}"/* "${tempDir}/"`);
  
  return tempDir;
}

// Деплой на Cloudflare Pages
function deployToCloudflare(tempDir) {
  try {
    // Создаем файл wrangler.toml для деплоя
    const wranglerConfig = `
name = "${config.projectName}"
account_id = "${config.accountId}"
compatibility_date = "2023-06-14"
main = "${tempDir}"
    `;
    
    fs.writeFileSync('wrangler.toml', wranglerConfig);
    
    // Выполняем деплой с помощью Wrangler
    console.log('Начинаем деплой...');
    execSync(`CLOUDFLARE_API_TOKEN=${config.apiToken} npx wrangler pages publish "${tempDir}" --project-name=${config.projectName}`, { stdio: 'inherit' });
    
    console.log('Деплой успешно завершен');
    
    // Удаляем временные файлы
    fs.unlinkSync('wrangler.toml');
    execSync(`rm -rf "${tempDir}"`);
    
    return true;
  } catch (err) {
    console.error('Ошибка при деплое:', err);
    return false;
  }
}

// Основная функция
async function main() {
  console.log('=== Утилита деплоя на Cloudflare Pages ===');
  
  // Загружаем конфигурацию или настраиваем авторизацию
  if (!loadConfig()) {
    await setupAuth();
  }
  
  // Проверяем авторизацию
  if (!checkAuth()) {
    console.log('Требуется повторная настройка авторизации');
    await setupAuth();
    if (!checkAuth()) {
      console.error('Не удалось авторизоваться. Проверьте учетные данные.');
      rl.close();
      return;
    }
  }
  
  // Получаем список подпапок
  const subfolders = getSubfolders();
  if (subfolders.length === 0) {
    console.error('Не найдены подпапки в static_assets');
    rl.close();
    return;
  }
  
  console.log('Доступные папки для деплоя:');
  subfolders.forEach((folder, index) => {
    console.log(`${index + 1}. ${folder}`);
  });
  
  // Выбор папки для деплоя
  rl.question('Выберите номер папки для деплоя: ', async (answer) => {
    const index = parseInt(answer) - 1;
    if (isNaN(index) || index < 0 || index >= subfolders.length) {
      console.error('Неверный выбор');
      rl.close();
      return;
    }
    
    const selectedFolder = subfolders[index];
    console.log(`Выбрана папка: ${selectedFolder}`);
    
    // Создаем временную папку и выполняем деплой
    const tempDir = createTempFolder(selectedFolder);
    await deployToCloudflare(tempDir);
    
    rl.close();
  });
}

// Запуск основной функции
main();

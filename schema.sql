-- схема для таблицы user_visits_log в Cloudflare D1

DROP TABLE IF EXISTS user_visits_log; -- Удаляем таблицу, если она уже существует (для чистого создания)

CREATE TABLE user_visits_log (
    timestamp TEXT NOT NULL,                    -- Время визита в формате ISO 8601
    cf_ray TEXT PRIMARY KEY NOT NULL,           -- Уникальный идентификатор запроса Cloudflare
    offer_id TEXT NOT NULL,                     -- Идентификатор оффера (субдомен)
    variant_shown TEXT NOT NULL,                -- Показанный вариант ("a" или "b")
    country_code TEXT,                          -- Двухбуквенный код страны
    client_ip TEXT,                             -- IP-адрес клиента
    client_trust_category TEXT,                 -- Категория доверия клиенту (эвристика)
    as_organization TEXT,                       -- Название организации-владельца AS
    device_type TEXT,                           -- Тип устройства ("mobile", "desktop", "tablet", "unknown")
    is_webview BOOLEAN,                         -- true, если определено как WebView, иначе false
    webview_app_guess TEXT,                     -- Предполагаемое приложение WebView ("Facebook", "Instagram", "GoogleApp", "Android System WebView", "iOS System WebView", "Other", "N/A")
    os_name TEXT,                               -- Название операционной системы
    browser_name TEXT,                          -- Название браузера
    user_agent_raw TEXT,                        -- Полная строка User-Agent
    all_headers_raw TEXT,                       -- JSON-строка всех заголовков запроса
    cf_object_raw TEXT,                         -- JSON-строка всего объекта request.cf
    request_url TEXT,                           -- Полный URL запроса
    filter_passed_reason TEXT                   -- Причина фильтрации или "passed_all_filters"
);

-- Опционально: можно добавить индексы для часто используемых полей для ускорения запросов
-- Например, если часто будете фильтровать по offer_id и timestamp:
CREATE INDEX IF NOT EXISTS idx_offer_id_timestamp ON user_visits_log (offer_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_country_code ON user_visits_log (country_code);
CREATE INDEX IF NOT EXISTS idx_variant_shown ON user_visits_log (variant_shown);
CREATE INDEX IF NOT EXISTS idx_webview_app_guess ON user_visits_log (webview_app_guess);
CREATE INDEX IF NOT EXISTS idx_filter_passed_reason  ON user_visits_log (filter_passed_reason );
// worker-offer1.js (Пример для offer1.mirus.help)

// --- КОНФИГУРАЦИЯ WORKER'А (Редактируется для каждого оффера) ---
const OFFER_ID = "enzimlex";
const ASSETS_BASE_URL = "https://assets.mirus.help";

const PATH_VARIANT_A = `/${OFFER_ID}/a/index.html`;
const PATH_VARIANT_B = `/${OFFER_ID}/b/index.html`;

const filterSettings = {
    FORCE_VARIANT_B: false,
    country: {
        enabled: true,
        logic: "allowed", 
        list: ["MX", "CO", "ES"],
        sendUnknownToB: true,
    },
    asOrganization: {
        enabled: true,
        disallowed: [
            "Google LLC", "Google Cloud", "GOOGLE",
            "Amazon.com, Inc.", "Amazon Technologies Inc.", "AWS", "AMAZON-02",
            "Microsoft Corporation", "Microsoft Azure", "MICROSOFT-CORP-MSN-AS-BLOCK",
            "OVH SAS", "Hetzner Online GmbH", "DigitalOcean, LLC", "Linode, LLC",
            "Cloudflare, Inc.", "AS-CHOOPA", "Psychz Networks", " FranTech Solutions"
        ],
    },
    clientTrust: {
        enabled: false,
        blockIfScoreLessThan: 15,
    },
    os: {
        enabled: true,
        logic: "allowed",
        list: ["Android", "iOS", "Windows"],
    },
    isWebview: { // Общая проверка на WebView
        enabled: true,
        required: true, // true = только WebView может пройти на A
    },
    isFacebookApp: { // Проверка, является ли это приложением Facebook (включая Messenger, Instagram)
        enabled: true,
        required: true, // true = только приложения FB/Insta/Messenger могут пройти на A (если isWebview также true)
    }
};
// --- КОНЕЦ КОНФИГУРАЦИИ WORKER'А ---

export default {
    async fetch(request, env, ctx) {
        const timestamp = new Date().toISOString();
        const cfRay = request.headers.get('cf-ray') || `no-ray-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const requestUrl = request.url;

        // 1. Сбор данных о пользователе
        const cfData = request.cf || {};
        const countryCode = cfData.country || null;
        const clientIp = request.headers.get('CF-Connecting-IP') || "UNKNOWN_IP";
        const asOrganization = cfData.asOrganization || "Unknown AS Organization";
        const clientTrustScore = cfData.clientTrustScore || null;
        
        const userAgentRaw = request.headers.get('User-Agent') || "";
        
        const headersObject = {};
        for (const [key, value] of request.headers) {
            headersObject[key] = value;
        }
        const allHeadersRaw = JSON.stringify(headersObject);
        const cfObjectRaw = JSON.stringify(cfData);

        const uaInfo = parseUserAgent(userAgentRaw);
        
        let clientTrustCategory = "unknown";
        if (clientTrustScore !== null) {
            if (clientTrustScore < 15) clientTrustCategory = "very_low_trust";
            else if (clientTrustScore < 50) clientTrustCategory = "low_trust";
            else clientTrustCategory = "high_trust";
        }

        let targetVariantPath = PATH_VARIANT_A;
        let filterPassedReason = "passed_all_filters";
        let variantDecision = "a";

        // 2. Логика Фильтрации и Выбора Варианта
        if (filterSettings.FORCE_VARIANT_B) {
            targetVariantPath = PATH_VARIANT_B;
            filterPassedReason = "forced_variant_b";
        } else {
            // Последовательная проверка фильтров
            let reasonForB = null;

            if (filterSettings.country.enabled) {
                const countryKnown = countryCode && countryCode !== "T1";
                if (!countryKnown && filterSettings.country.sendUnknownToB) {
                    reasonForB = "country_unknown";
                } else if (countryKnown) {
                    const listContainsCountry = filterSettings.country.list.includes(countryCode);
                    if (filterSettings.country.logic === "allowed" && !listContainsCountry) {
                        reasonForB = "country_not_in_allowed_list";
                    } else if (filterSettings.country.logic === "disallowed" && listContainsCountry) {
                        reasonForB = "country_in_disallowed_list";
                    }
                }
            }

            if (!reasonForB && filterSettings.asOrganization.enabled && asOrganization) {
                const lowerCaseAsOrg = asOrganization.toLowerCase();
                if (filterSettings.asOrganization.disallowed.some(org => lowerCaseAsOrg.includes(org.toLowerCase()))) {
                    reasonForB = "as_organization_blocked";
                }
            }
            
            if (!reasonForB && filterSettings.clientTrust.enabled && clientTrustScore !== null) {
                if (clientTrustScore < filterSettings.clientTrust.blockIfScoreLessThan) {
                    reasonForB = `client_trust_score_too_low (${clientTrustScore})`;
                }
            }

            if (!reasonForB && filterSettings.os.enabled && uaInfo.os.name !== "unknown") {
                const lowerCaseOSList = filterSettings.os.list.map(os => os.toLowerCase());
                const currentOSLower = uaInfo.os.name.toLowerCase();
                const listContainsOS = lowerCaseOSList.includes(currentOSLower);

                if (filterSettings.os.logic === "allowed" && !listContainsOS) {
                    reasonForB = "os_not_in_allowed_list";
                } else if (filterSettings.os.logic === "disallowed" && listContainsOS) {
                    reasonForB = "os_in_disallowed_list";
                }
            }

            if (!reasonForB && filterSettings.isWebview.enabled) {
                if (uaInfo.browser.isWebview !== filterSettings.isWebview.required) {
                    reasonForB = `is_webview_mismatch (required: ${filterSettings.isWebview.required}, actual: ${uaInfo.browser.isWebview})`;
                }
            }
            
            // Этот фильтр имеет смысл, только если isWebview.required = true и предыдущий фильтр не сработал
            if (!reasonForB && filterSettings.isFacebookApp.enabled && filterSettings.isWebview.required && uaInfo.browser.isWebview) {
                 if (uaInfo.browser.isFacebookApp !== filterSettings.isFacebookApp.required) {
                    reasonForB = `facebook_app_mismatch (required: ${filterSettings.isFacebookApp.required}, actual: ${uaInfo.browser.isFacebookApp})`;
                }
            }

            if (reasonForB) {
                targetVariantPath = PATH_VARIANT_B;
                filterPassedReason = reasonForB;
            }
        }

        variantDecision = (targetVariantPath === PATH_VARIANT_A) ? "a" : "b";

        // 3. Формирование URL к HTML-файлу и его получение
        const finalHtmlUrl = ASSETS_BASE_URL + targetVariantPath;
        let responseToClient;

        try {
            const assetResponse = await fetch(finalHtmlUrl);
            if (assetResponse.ok) {
                responseToClient = new Response(assetResponse.body, assetResponse);
                responseToClient.headers.set("Content-Type", "text/html;charset=UTF-8");
            } else {
                filterPassedReason = `asset_fetch_error_(${assetResponse.status})_for_${targetVariantPath}`;
                responseToClient = new Response(
                    `Error: Content not found (status ${assetResponse.status}). Ray ID: ${cfRay}`,
                    { status: assetResponse.status, headers: { "Content-Type": "text/html;charset=UTF-8" } }
                );
            }
        } catch (error) {
            console.error(`[${cfRay}] Error fetching asset ${finalHtmlUrl}: ${error.message}`, error.stack);
            filterPassedReason = `asset_fetch_exception_for_${targetVariantPath}`;
            responseToClient = new Response(
                `Server Error. Ray ID: ${cfRay}`,
                { status: 500, headers: { "Content-Type": "text/html;charset=UTF-8" } }
            );
        }
        
        // 4. Логирование (асинхронно)
        ctx.waitUntil(
            logVisit(env, {
                timestamp, cfRay, offerId: OFFER_ID, variantShown: variantDecision, countryCode, clientIp,
                clientTrustCategory, asOrganization, 
                deviceType: uaInfo.device.type, 
                isWebview: uaInfo.browser.isWebview, 
                webviewAppGuess: uaInfo.browser.webviewAppName,
                osName: uaInfo.os.name, 
                browserName: uaInfo.browser.name, 
                userAgentRaw, allHeadersRaw, cfObjectRaw,
                requestUrl, filterPassedReason
            })
        );

        return responseToClient;
    },
};

async function logVisit(env, data) {
    try {
        const stmt = env.USER_TRACKING_DB.prepare(
            `INSERT INTO user_visits_log (
                timestamp, cf_ray, offer_id, variant_shown, country_code, client_ip, 
                client_trust_category, as_organization, device_type, is_webview, webview_app_guess, 
                os_name, browser_name, user_agent_raw, all_headers_raw, cf_object_raw, 
                request_url, filter_passed_reason
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
        );
        await stmt.bind(
            data.timestamp, data.cfRay, data.offerId, data.variantShown, data.countryCode, data.clientIp,
            data.clientTrustCategory, data.asOrganization, data.deviceType, data.isWebview, data.webviewAppGuess,
            data.osName, data.browserName, data.userAgentRaw, data.allHeadersRaw, data.cfObjectRaw,
            data.requestUrl, data.filterPassedReason
        ).run();
    } catch (dbError) {
        console.error(`[${data.cfRay}] D1 Insert Error: ${dbError.message}`, dbError.cause ? dbError.cause.message : '', dbError.stack);
    }
}

// --- УЛУЧШЕННЫЙ ПАРСЕР USER-AGENT ---
// Это все еще эвристика, но с более детальными проверками.
// Для максимальной точности рассмотрите специализированные библиотеки, если возможно.

function parseUserAgent(ua) {
    const result = {
        browser: { name: "unknown", version: "unknown", isWebview: false, webviewAppName: "N/A", isFacebookApp: false },
        os: { name: "unknown", version: "unknown" },
        device: { type: "unknown", vendor: "unknown", model: "unknown" },
    };

    if (!ua || typeof ua !== 'string') return result;

    // Facebook App/WebView detection (приоритет)
    // FBAN/FBAV (Facebook App Name/Version), FBAN/MessengerForiOS, FBAN/Instagram
    // FB_IAB (Facebook In-App Browser), FBMF (Facebook Mobile Framework)
    // ;wv) - часто указывает на Android System WebView
    // Mobile).*wv - Android webview
    // Tinder, Pinterest, Snapchat также имеют свои маркеры
    if (/FBAN|FBAV|FB_IAB|FB4A|FBPN\/<y_bin_456>\.facebook\.katana|FBOP\/1|FBSN\/|FBMS\/|Messenger|Instagram|FBMF\/|FBBR\/|FBBD\/|FBBV\/|FBSV\/|FBCR\/|FBDM\/|FBBLK\/|FBLC\/|FBES\/|FBMA\/|FBCA\/|FBCT\/|FBCN\/|FBIOS\b/i.test(ua)) {
        result.browser.isWebview = true; // Приложения Facebook почти всегда WebView
        result.browser.isFacebookApp = true;
        if (/Messenger|Orca-Android|FBAN\/MessengerForiOS/i.test(ua)) {
            result.browser.webviewAppName = "Facebook Messenger";
        } else if (/Instagram/i.test(ua)) {
            result.browser.webviewAppName = "Instagram";
        } else {
            result.browser.webviewAppName = "Facebook";
        }
        // Версию приложения можно попытаться извлечь из FBAV/X.X.X
        const fbavMatch = ua.match(/FBAV\/([\d\.]+)/i);
        if (fbavMatch) result.browser.version = fbavMatch[1];
    }
    
    // Общий признак WebView для Android (если не определено как FB)
    if (!result.browser.isFacebookApp && /; wv\)|Mobile\).*wv|WebView|Crosswalk/i.test(ua)) {
        result.browser.isWebview = true;
        result.browser.webviewAppName = "Android System WebView"; // или другое, если есть более точные маркеры
    }
    // Google Search App (GSA)
    if (/GSA\/([\d\.]+)/i.test(ua)) {
        result.browser.isWebview = true;
        result.browser.webviewAppName = "Google Search App";
        result.browser.version = RegExp.$1;
    }


    // OS Detection
    if (/Windows NT 10\.0/i.test(ua)) { result.os.name = "Windows"; result.os.version = "10"; } 
    else if (/Windows NT 6\.3/i.test(ua)) { result.os.name = "Windows"; result.os.version = "8.1"; } 
    else if (/Windows NT 6\.2/i.test(ua)) { result.os.name = "Windows"; result.os.version = "8"; } 
    else if (/Windows NT 6\.1/i.test(ua)) { result.os.name = "Windows"; result.os.version = "7"; } 
    else if (/Windows Phone/i.test(ua)) { result.os.name = "Windows Phone"; const wpMatch = ua.match(/Windows Phone ([\d\.]+)/i); if (wpMatch) result.os.version = wpMatch[1];}
    else if (/Android(?:[\s\/]([\d\.]+))?/i.test(ua)) { result.os.name = "Android"; if(RegExp.$1) result.os.version = RegExp.$1; } 
    else if (/(iPhone|iPad|iPod)(?: OS ([\d_]+))?/i.test(ua)) { 
        result.os.name = "iOS"; 
        if(RegExp.$2) result.os.version = RegExp.$2.replace(/_/g, '.');
        result.device.vendor = "Apple";
        result.device.model = RegExp.$1;
    } 
    else if (/Mac OS X ([\d_]+)/i.test(ua) || /Macintosh;.*Mac OS X ([\d_]+)/i.test(ua)) { result.os.name = "macOS"; result.os.version = RegExp.$1.replace(/_/g, '.'); } 
    else if (/CrOS/i.test(ua)) { result.os.name = "Chrome OS"; }
    else if (/Linux/i.test(ua) && !/Android/i.test(ua)) { result.os.name = "Linux"; }

    // Browser Detection (после WebView, т.к. WebView может маскироваться)
    // Стараемся не переопределять, если это уже определено как конкретный WebView App
    if (result.browser.name === "unknown" || !result.browser.isWebview) {
        if (/(?:Edg|Edge|EdgA|EdgiOS)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Edge"; result.browser.version = RegExp.$1; } 
        else if (/(?:Chrome|CriOS|CrMo)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Chrome"; result.browser.version = RegExp.$1; } 
        else if (/(?:Firefox|FxiOS|Focus)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Firefox"; result.browser.version = RegExp.$1; }
        else if (/(?:MSIE |Trident\/.*; rv:)([\d\.]+)/i.test(ua)) { result.browser.name = "Internet Explorer"; result.browser.version = RegExp.$1; }
        else if (/Opera Mini\/([\d\.]+)/i.test(ua)) { result.browser.name = "Opera Mini"; result.browser.version = RegExp.$1;}
        else if (/(?:Opera|OPR)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Opera"; result.browser.version = RegExp.$1;}
        else if (/ начальника(?:Version\/([\d\.]+))?.*Safari\/([\d\.]+)/i.test(ua) && result.os.name === "iOS") { // Safari на iOS
            result.browser.name = "Safari";
            result.browser.version = RegExp.$1 || RegExp.$2; // Version/X.X или Safari/XXX.YY
        }
        else if (/Version\/([\d\.]+).*Safari\/([\d\.]+)/i.test(ua) && result.os.name === "macOS") { // Safari на macOS
            result.browser.name = "Safari";
            result.browser.version = RegExp.$1;
        }
        // Android WebView (если не FB и не GSA) часто выглядит как Chrome, но с wv
        if (result.os.name === "Android" && result.browser.name === "Chrome" && /\bwv\b/.test(ua)) {
            result.browser.isWebview = true;
            result.browser.webviewAppName = "Android System WebView";
        }
        // iOS WebView (если не FB и не GSA), если UA похож на Mobile Safari, но не известный браузер
        if (result.os.name === "iOS" && result.browser.name === "Safari" &&
            !/CriOS|FxiOS|EdgiOS|OPiOS/.test(ua) && // не Chrome, Firefox, Edge, Opera
            !result.browser.isFacebookApp // и не Facebook
            ) {
            // Сложно отличить от Safari без дополнительных маркеров.
            // Некоторые приложения добавляют свой маркер к UA WKWebView.
            // Если есть маркер приложения, то это WebView. Если нет, это может быть Safari или WKWebView.
            // Для простоты, если нет явных признаков другого браузера, считаем WKWebView
            const knowniOSBrowsers = /\b(CriOS|FxiOS|EdgiOS|OPiOS|Focus|Vivaldi|Brave|DuckDuckGo)\b/i;
            if(!knowniOSBrowsers.test(ua)) {
                 result.browser.isWebview = true;
                 result.browser.webviewAppName = "iOS System WebView"; // или имя приложения, если есть маркер
            }
        }
    }


    // Device Type
    if (result.os.name === "Android" || result.os.name === "iOS" || result.os.name === "Windows Phone" || /Mobi/i.test(ua)) {
        if (/Tablet|iPad|PlayBook/i.test(ua) || (result.os.name === "Android" && !/Mobile/i.test(ua))) {
            result.device.type = "tablet";
        } else {
            result.device.type = "mobile";
        }
    } else if (result.os.name === "Windows" || result.os.name === "macOS" || result.os.name === "Linux" || result.os.name === "Chrome OS") {
        result.device.type = "desktop";
    }
    if (result.device.model === "iPad") result.device.type = "tablet";


    // Если это FB приложение, но isWebview не установился из-за специфичного UA, принудительно ставим
    if (result.browser.isFacebookApp && !result.browser.isWebview) {
        result.browser.isWebview = true;
    }
    // Если webviewAppName не N/A, то это точно webview
    if (result.browser.webviewAppName !== "N/A" && !result.browser.isWebview) {
        result.browser.isWebview = true;
    }


    return result;
} 
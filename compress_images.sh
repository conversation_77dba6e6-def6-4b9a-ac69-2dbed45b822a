#!/bin/bash

# Скрипт для сжатия изображений с помощью ImageMagick
# Автор: Augment Agent

# Путь к папке с изображениями
IMAGE_DIR="./static_assets/enzimlex/b/images"

# Создаем папку для резервных копий
BACKUP_DIR="${IMAGE_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Копируем все оригинальные изображения в резервную папку
echo "Создание резервной копии изображений в $BACKUP_DIR..."
cp -r "$IMAGE_DIR"/* "$BACKUP_DIR"/

# Функция для получения размера файла в килобайтах
get_file_size() {
    stat -f %z "$1" | awk '{print int($1/1024)}'
}

# Функция для сжатия JPEG изображений
compress_jpeg() {
    local file="$1"
    local original_size=$(get_file_size "$file")

    # Сжимаем изображение с качеством 85%
    magick "$file" -strip -interlace Plane -gaussian-blur 0.05 -quality 85% "$file.tmp"

    # Проверяем новый размер
    local new_size=$(get_file_size "$file.tmp")
    local reduction=$((original_size - new_size))

    # Избегаем деления на ноль
    local percent=0
    if [ $original_size -gt 0 ]; then
        percent=$((reduction * 100 / original_size))
    fi

    # Заменяем оригинал только если сжатие эффективно
    if [ $new_size -lt $original_size ]; then
        mv "$file.tmp" "$file"
        echo "  Сжато: $file - было: ${original_size}KB, стало: ${new_size}KB (уменьшение на ${percent}%)"
    else
        rm "$file.tmp"
        echo "  Пропущено: $file - сжатие не дало эффекта"
    fi
}

# Функция для сжатия PNG изображений
compress_png() {
    local file="$1"
    local original_size=$(get_file_size "$file")

    # Сжимаем PNG с оптимизацией
    # Используем более эффективное сжатие для PNG
    magick "$file" -strip -define png:compression-level=9 -define png:compression-strategy=3 -define png:exclude-chunk=all -quality 85% "$file.tmp"

    # Проверяем новый размер
    local new_size=$(get_file_size "$file.tmp")
    local reduction=$((original_size - new_size))

    # Избегаем деления на ноль
    local percent=0
    if [ $original_size -gt 0 ]; then
        percent=$((reduction * 100 / original_size))
    fi

    # Заменяем оригинал только если сжатие эффективно
    if [ $new_size -lt $original_size ]; then
        mv "$file.tmp" "$file"
        echo "  Сжато: $file - было: ${original_size}KB, стало: ${new_size}KB (уменьшение на ${percent}%)"
    else
        rm "$file.tmp"
        echo "  Пропущено: $file - сжатие не дало эффекта"
    fi
}

# Функция для сжатия GIF изображений
compress_gif() {
    local file="$1"
    local original_size=$(get_file_size "$file")

    # Сжимаем GIF с оптимизацией
    magick "$file" -strip -optimize "$file.tmp"

    # Проверяем новый размер
    local new_size=$(get_file_size "$file.tmp")
    local reduction=$((original_size - new_size))

    # Избегаем деления на ноль
    local percent=0
    if [ $original_size -gt 0 ]; then
        percent=$((reduction * 100 / original_size))
    fi

    # Заменяем оригинал только если сжатие эффективно
    if [ $new_size -lt $original_size ]; then
        mv "$file.tmp" "$file"
        echo "  Сжато: $file - было: ${original_size}KB, стало: ${new_size}KB (уменьшение на ${percent}%)"
    else
        rm "$file.tmp"
        echo "  Пропущено: $file - сжатие не дало эффекта"
    fi
}

# Получаем общий размер папки до сжатия
BEFORE_SIZE=$(du -sk "$IMAGE_DIR" | cut -f1)
echo "Размер папки до сжатия: ${BEFORE_SIZE}KB"

# Обрабатываем все изображения
echo "Начинаем сжатие изображений..."

# JPEG изображения
echo "Обработка JPEG изображений..."
find "$IMAGE_DIR" -type f \( -iname "*.jpg" -o -iname "*.jpeg" \) | while read file; do
    compress_jpeg "$file"
done

# PNG изображения
echo "Обработка PNG изображений..."
find "$IMAGE_DIR" -type f -iname "*.png" | while read file; do
    compress_png "$file"
done

# GIF изображения
echo "Обработка GIF изображений..."
find "$IMAGE_DIR" -type f -iname "*.gif" | while read file; do
    compress_gif "$file"
done

# Получаем общий размер папки после сжатия
AFTER_SIZE=$(du -sk "$IMAGE_DIR" | cut -f1)
REDUCTION=$((BEFORE_SIZE - AFTER_SIZE))

# Избегаем деления на ноль
PERCENT=0
if [ $BEFORE_SIZE -gt 0 ]; then
    PERCENT=$((REDUCTION * 100 / BEFORE_SIZE))
fi

echo "Сжатие завершено!"
echo "Размер папки до сжатия: ${BEFORE_SIZE}KB"
echo "Размер папки после сжатия: ${AFTER_SIZE}KB"
echo "Уменьшение размера: ${REDUCTION}KB (${PERCENT}%)"
echo "Резервная копия оригинальных изображений сохранена в: $BACKUP_DIR"
